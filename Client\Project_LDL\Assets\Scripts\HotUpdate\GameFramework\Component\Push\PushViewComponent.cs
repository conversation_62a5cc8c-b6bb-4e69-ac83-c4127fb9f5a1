using System;
using System.Collections.Generic;
using UnityEngine;
using UnityGameFramework.Runtime;
using Game.Hotfix.Config;
using GameFramework.Event;

namespace Game.Hotfix
{
    [DisallowMultipleComponent]
    [AddComponentMenu("GameCustom/PushViewComponent")]
    public class PushViewComponent : GameFrameworkComponent
    {
        /// <summary>
        /// 推送界面数据
        /// </summary>
        class PushViewData
        {
            public EnumUIForm enumUIForm;  // 界面枚举
            public object userData;        // 界面参数
        }

        readonly List<PushViewData> pushViewDatas = new();  // 推送界面数据列表
        bool isPushing = false;                             // 是否推送中
        PushViewData curPushViewData;                       // 当前推送界面数据

        // 忽略在顶层的界面
        readonly List<string> ignoreUIForms = new()
        {
            "UIClickEffectForm", "UIResFlyForm", "UIFlyTextForm"
        };

        void Start()
        {
            GameEntry.Event.Subscribe(CloseUIFormCompleteEventArgs.EventId, OnCloseUIFormCompleteEventArgs);
        }

        void Update()
        {
            // 没有数据或者正在推送中
            if (pushViewDatas.Count == 0 || isPushing) return;

            // 主界面不在最顶层
            if (!CheckUIFormOnTop<UIMainFaceForm>()) return;

            // 进入推送中状态
            isPushing = true;

            // 取出第一个推送界面数据并打开界面
            curPushViewData = pushViewDatas[0];
            GameEntry.UI.OpenUIForm(curPushViewData.enumUIForm, curPushViewData.userData);
        }

        void OnDestroy()
        {
            if (GameEntry.Event.Check(CloseUIFormCompleteEventArgs.EventId, OnCloseUIFormCompleteEventArgs))
                GameEntry.Event.Unsubscribe(CloseUIFormCompleteEventArgs.EventId, OnCloseUIFormCompleteEventArgs);
        }

        void OnCloseUIFormCompleteEventArgs(object sender, GameEventArgs e)
        {
            if (e is CloseUIFormCompleteEventArgs args)
            {
                // 界面资源路径
                string assetName = args.UIFormAssetName;

                // 裁剪出界面名称
                int startIndex = assetName.LastIndexOf('/') + 1;
                int endIndex = assetName.LastIndexOf('.');
                string name = assetName[startIndex..endIndex];

                // 字符串转枚举
                EnumUIForm enumUIForm = (EnumUIForm)Enum.Parse(typeof(EnumUIForm), name);

                if (curPushViewData?.enumUIForm == enumUIForm)
                {
                    pushViewDatas.Remove(curPushViewData);
                    isPushing = false;
                }
            }
        }

        /// <summary>
        /// 添加推送界面
        /// </summary>
        /// <param name="pushId">推送 id</param>
        /// <param name="userData">界面参数</param>
        public void AddPushView(int pushId, object userData)
        {
            var pushViewConfig = GameEntry.LDLTable.GetTableById<push_manage>(pushId);
            if (pushViewConfig == null) return;
            EnumUIForm enumUIForm = (EnumUIForm)Enum.Parse(typeof(EnumUIForm), pushViewConfig.prefab_name);
            AddPushView(enumUIForm, userData);
        }

        /// <summary>
        /// 添加推送界面
        /// </summary>
        /// <param name="enumUIForm">界面枚举</param>
        /// <param name="userData">界面参数</param>
        public void AddPushView(EnumUIForm enumUIForm, object userData)
        {
            pushViewDatas.Add(new PushViewData() { enumUIForm = enumUIForm, userData = userData });
        }

        /// <summary>
        /// 移除推送界面
        /// </summary>
        /// <param name="enumUIForm">界面枚举</param>
        public void RemovePushView(EnumUIForm enumUIForm)
        {
            for (int i = pushViewDatas.Count - 1; i >= 0; i--)
            {
                if (pushViewDatas[i].enumUIForm == enumUIForm)
                {
                    pushViewDatas.RemoveAt(i);
                }
            }
        }

        /// <summary>
        /// 清除推送界面
        /// </summary>
        public void ClearPushView()
        {
            pushViewDatas.Clear();
        }

        /// <summary>
        /// 检查界面是否在最顶层
        /// </summary>
        /// <returns></returns>
        bool CheckUIFormOnTop<TUIForm>()
        {
            List<UIForm> uiForms = new();

            // 获取所有已加载的界面
            GameEntry.UI.GetAllLoadedUIForms(uiForms);

            // 层级排序
            uiForms.Sort((a, b) =>
            {
                // 先排序组
                if (a.UIGroup.Depth != b.UIGroup.Depth)
                {
                    return a.UIGroup.Depth.CompareTo(b.UIGroup.Depth);
                }
                // 再排序组内深度
                else if (a.DepthInUIGroup != b.DepthInUIGroup)
                {
                    return a.DepthInUIGroup.CompareTo(b.DepthInUIGroup);
                }
                return 0;
            });

            for (int i = uiForms.Count - 1; i >= 0; i--)
            {
                string uiFormName = uiForms[i].Logic.ToString();
                int startIndex = 0;
                int endIndex = uiFormName.IndexOf('(');
                string name = uiFormName[startIndex..endIndex];
                if (ignoreUIForms.Contains(name))
                {
                    continue;
                }

                if (uiForms[i].Logic is TUIForm)
                {
                    return true;
                }
                else
                {
                    return false;
                }
            }

            return false;
        }
    }
}
