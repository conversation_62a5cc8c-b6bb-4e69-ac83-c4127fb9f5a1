using GameFramework.Event;
using UnityEngine;

namespace Game.Hotfix
{
    public class ED_TownMovePreview : EntityData
    {
        public Vector2Int TargetGridPos;
        
        public ED_TownMovePreview(int entityId,Vector2Int targetGridPos) : base(entityId)
        {
            TargetGridPos = targetGridPos;
        }
    }
    
    public class EL_TownMovePreview : Entity
    {
        private const int SIZE = 3;
        private ED_TownMovePreview m_Data;
        
        private GameObject m_BuildingContainer;
        private bool m_GridCanPut;
        
        //提示相关
        private eBuildingPlacementRejectionReason m_LastRejectReason = eBuildingPlacementRejectionReason.Unknown;
        private ulong m_LastShowReasonTime = 0;
        private const ulong ShowReasonInterval = 1;

        private bool m_NeedReCheck = false;
        
        protected override void OnInit(object userData)
        {
            base.OnInit(userData);
            
        }

        protected override void OnShow(object userData)
        {
            base.OnShow(userData);
            
            m_Data = (ED_TownMovePreview)userData;
            

            var pos = m_Data.TargetGridPos;
            SetGridPos(pos);

            BuildingGridDisplay gridDisplay = gameObject.GetOrAddComponent<BuildingGridDisplay>();
            gridDisplay.SetSize(SIZE, SIZE, -SIZE / 2f+0.5f, -SIZE / 2f+0.5f);

            m_BuildingContainer = transform.Find("BuildingContainer").gameObject;

            GameEntry.Event.Subscribe(OnWorldMapUnBuildableDataUpdateArgs.EventId,OnWorldMapUnBuildableDataUpdate);
            
            m_NeedReCheck = true;
        }

        private void OnWorldMapUnBuildableDataUpdate(object sender, GameEventArgs e)
        {
            m_NeedReCheck = true;
        }

        protected override void OnHide(bool isShutdown, object userData)
        {
            base.OnHide(isShutdown, userData);
            GameEntry.Event.Unsubscribe(OnWorldMapUnBuildableDataUpdateArgs.EventId,OnWorldMapUnBuildableDataUpdate);
        }

        protected override void OnRecycle()
        {
            base.OnRecycle();
        }

        public void SetGridPos(Vector2Int pos)
        {   
            transform.SetPositionX(pos.x);
            transform.SetPositionZ(pos.y);

            m_NeedReCheck = true;
        }

        public bool IsInGrid(int x,int y)
        {
            var tempPos = MapGridUtils.WorldToGrid(transform.position);
            return IsInArea(x, y, tempPos.x, tempPos.y);
        }

        private void Update()
        {
            if (m_NeedReCheck)
            {
                m_NeedReCheck = false;
                ValidateGridCanPut();
            }
        }

        public void ValidateGridCanPut()
        {
            var tempPos = MapGridUtils.WorldToGrid(transform.position);
            float offset = -SIZE / 2f + 0.5f;
            Rect rect = new Rect(tempPos.x+offset, tempPos.y+offset, SIZE, SIZE);
            
            var xL = (int)rect.xMin;
            var xR = (int)rect.xMax;
            var yB = (int)rect.yMin;
            var yT = (int)rect.yMax;
            
            //检测是否可以建造
            bool canPut = true;
            
            for (int i = xL; i < xR; i++)
            {
                for (int j = yB; j < yT; j++)
                {
                    if(!GameEntry.WorldMap.CanPutTownHere(new Vector2Int(i, j)))
                    {
                        canPut = false;
                        break;
                    }        
                }
            }
            BuildingGridDisplay gridDisplay = gameObject.GetOrAddComponent<BuildingGridDisplay>();
            gridDisplay.SetEnable(canPut);
            m_GridCanPut = canPut;
        }

        private bool IsInArea(int x, int y, int baseX, int baseY)
        {
            float offset = -SIZE / 2f + 0.5f;
            
            Rect rect = new Rect(baseX+offset, baseY+offset, SIZE, SIZE);
            if (rect.Contains(new Vector2(x, y)))
            {
                return true;
            }

            return false;
        }
        
        public bool IsGridCanPut()
        {
            return m_GridCanPut;
        }
    }
}