using System.Collections.Generic;
using GameFramework;
using GameFramework.Event;
using UnityEngine;

namespace Game.Hotfix
{
    public class OnWorldMapUnBuildableDataUpdateArgs : GameEventArgs
    {
        public static readonly int EventId = typeof(OnWorldMapUnBuildableDataUpdateArgs).GetHashCode();

        public OnWorldMapUnBuildableDataUpdateArgs()
        {
        }

        public override int Id
        {
            get { return EventId; }
        }


        public static OnWorldMapUnBuildableDataUpdateArgs Create()
        {
            OnWorldMapUnBuildableDataUpdateArgs e = ReferencePool.Acquire<OnWorldMapUnBuildableDataUpdateArgs>();
            return e;
        }

        public override void Clear()
        {
        }
    }
}