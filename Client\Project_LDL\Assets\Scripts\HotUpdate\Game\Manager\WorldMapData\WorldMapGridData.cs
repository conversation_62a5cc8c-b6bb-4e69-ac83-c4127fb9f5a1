using System.Collections.Generic;
using System.Linq;
using Game.Hotfix.Config;
using UnityEngine;
using Wmcamera;
using map_element_big_type = PbGameconfig.map_element_big_type;

namespace Game.Hotfix
{
    public enum WorldMapGridDataEvent
    {
        TownDataAdd,
        TownDataUpdate,
        TownDataDelete,
        
        MyTownDataAdd,
        MyTownDataUpdate,
        MyTownDataDelete
        
    }
    
    public class WorldMapGridData
    {
        public List<Vector3Int> CityData => m_CityData;
        public List<GridTownData> TownData => m_TownData;
        public List<GridMonsterData> MonsterData => m_MonsterData;
        public List<GridFortsData> FortsData => m_FortsData;
        public List<GridMineData> MineData => m_MineData;

        private Dictionary<int, GridMineData> MineDataDic;
        
        private EventDispatch<WorldMapGridDataEvent> m_EventDispatch = new EventDispatch<WorldMapGridDataEvent>();
        
        private int m_GridX;
        private int m_GridY;
        private int m_LogicId;
        
        /// <summary>
        /// 城市数据
        /// </summary>
        private List<Vector3Int> m_CityData;

        private List<GridTownData> m_TownData;
        private List<GridMonsterData> m_MonsterData;
        private List<GridFortsData> m_FortsData;
        private List<GridMineData> m_MineData;
        
        public static WorldMapGridData Create(int x, int y)
        {   
            var t = new WorldMapGridData(x,y);
            return t;
        }

        private WorldMapGridData(int x, int y)
        {
            m_GridX = x;
            m_GridY = y;
            m_LogicId = MapGridUtils.GridId2ScreenId(m_GridX, m_GridY);

            m_CityData = new List<Vector3Int>();
            m_TownData = new List<GridTownData>();
            m_MonsterData = new List<GridMonsterData>();
            m_FortsData = new List<GridFortsData>();
            m_MineData = new List<GridMineData>();
        }

        public void AddCity(int posX,int posY,int cityId)
        {
            var data = new Vector3Int(posX, posY, cityId);
            m_CityData.Add(data);
        }

        public void AddMonster(MapInfo mapInfo)
        {
            var data = new GridMonsterData(mapInfo);
            m_MonsterData.Add(data);
        }

        public void AddForts(MapInfo mapInfo)
        {
            var data = new GridFortsData(mapInfo);
            m_FortsData.Add(data);
        }

        public void AddMine(MapInfo mapInfo)
        {
            var data = new GridMineData(mapInfo);
            MineData.Add(data);
        }
        
        public GridTownData AddTown(MapInfo mapInfo)
        {
            var data = new GridTownData(mapInfo);
            TownData.Add(data);
            return data;
        }

        public void OnPushMapInfo(MapInfo mapInfo)
        {
            var mapID = mapInfo.MapId;
            if(mapInfo.BigType == map_element_big_type._4)
            {
                //主城
                if (mapInfo.RoleId == 0)//TODO 临时 mapInfo.RoleId 为0 表示没有人的主城 删除掉
                {
                    //delete
                    var targetData = m_TownData.Find(data => data.MapID == mapID);
                    if (targetData != null)
                    {
                        m_TownData.Remove(targetData);
                        if(targetData.IsMyTown)
                            SendEvent(WorldMapGridDataEvent.MyTownDataDelete, targetData);
                        else
                            SendEvent(WorldMapGridDataEvent.TownDataDelete, targetData);
                    }
                }else
                {
                    var targetData = m_TownData.Find(data => data.MapID == mapID);
                    if (targetData!=null)
                    {
                        //update
                        targetData.Update(mapInfo);
                        if(targetData.IsMyTown)
                            SendEvent(WorldMapGridDataEvent.MyTownDataUpdate, targetData);
                        else
                            SendEvent(WorldMapGridDataEvent.TownDataUpdate, targetData);
                    }
                    else
                    {
                        //add
                        var data = AddTown(mapInfo);
                        if(data.IsMyTown)
                            SendEvent(WorldMapGridDataEvent.MyTownDataAdd, data);
                        else
                            SendEvent(WorldMapGridDataEvent.TownDataAdd, data);
                    }
                }
            }
        }
        
        
        public List<Rect> GetUnBuildArea()
        {
            List<Rect> list = new List<Rect>();
            //city
            foreach (var cityData in m_CityData)
            {
                map_city cityCfg = GameEntry.LDLTable.GetTableById<map_city>(cityData.z);
                if (cityCfg != null)
                {
                    Rect rect = new Rect(cityData.x, cityData.y, cityCfg.city_grid, cityCfg.city_grid);
                    list.Add(rect);    
                }
            }
            //town
            foreach (var townData in m_TownData)
            {
                Rect rect = new Rect(townData.PosX, townData.PosY, GameDefine.WorldMapTownSize.x,
                    GameDefine.WorldMapTownSize.y);
                list.Add(rect);
            }

            //mine
            foreach (var data in m_MineData)
            {
                Rect rect = new Rect(data.PosX, data.PosY, 1, 1);
                list.Add(rect);
            }
            
            return list;
        }

        public void Clear()
        {
            TownData.Clear();
            ClearElement();
        }
        
        public void ClearElement()
        {
            MonsterData.Clear();
            FortsData.Clear();
            MineData.Clear();
        }
        
        
        #region 事件

        public void AddEventListener(WorldMapGridDataEvent eventType, EventCallBack eventHandler)
        {
            m_EventDispatch.RegisterEvent(eventType, eventHandler);
        }

        public void RemoveEventListener(WorldMapGridDataEvent eventType, EventCallBack eventHanlder)
        {
            m_EventDispatch.UnRegisterEvent(eventType, eventHanlder);
        }

        protected void SendEvent(WorldMapGridDataEvent eventType, object obj)
        {
            m_EventDispatch.PostEvent(eventType, obj);
        }

        #endregion
        
    }
}
