using System.Linq;
using Game.Hotfix.Config;
using UnityEngine;
using UnityEngine.UI;

namespace Game.Hotfix
{
    //超级月卡
    public class Mall_MonthCard:SwitchPanelLogic
    {
        public UIImage m_imgDiamond;
        public UIText m_txtDiamondCount;
        public Transform m_transNowGet;
        public Transform m_transDailyGet;
        public UIButton m_btnBuy;
        public UIText m_txtPrice;
        public GameObject m_goDiscount;
        public UIText m_txtDiscount;
        public UIText m_txtLevel;
        
        public GameObject m_goStatus0;
        public GameObject m_goStatus1;
        public UIButton m_btnGet;
        public GameObject m_goStatus2;
        public UIButton m_btnSoldOut;
        public UIText m_txtAddTip;
        
        public UIText m_txtRemainDays;

        public GameObject m_goPrefab;
        public GameObject m_goRewardItem;
        
        public UIText m_txtDayTip;
        public UIText m_txtRemainCount;

        public RectTransform m_goDescRoot;
        public RectTransform m_goBtnGroup;

        public GameObject m_goEffect;
        public GameObject mainIcon;
        
        private MallData Manager;
        //实例化对象时初始化
        public override void OnInit()
        {
            base.OnInit();
            m_goPrefab.SetActive(false);
            Manager = GameEntry.LogicData.MallData;
            InitPageConfigView();
            CheckMultiLanguage(gameObject);
            Manager.RequestNewMsg(paymenttype.paymenttype_monthycard,InitPageView);
        }
        
        //再次打开
        public override void OnReOpen()
        {
            Manager.RequestNewMsg(paymenttype.paymenttype_monthycard,InitPageView);
        }
        
        //本界面刷新，一般由UISwitchPage.RefreshCurPage触发
        public override void OnRefreshSelf()
        {
            base.OnRefreshSelf();
            Manager.RequestNewMsg(paymenttype.paymenttype_monthycard,InitPageView);
        }
        
        //事件刷新
        public override void OnRefresh(object userData)
        {
            
        }

        /// <summary>
        /// 被选中触发的逻辑
        /// </summary>
        /// <param name="isOn">是否被选中</param>
        public override void OnSelect(bool isOn)
        {
            
        }
        
        //隐藏面板时逻辑
        public override void OnClose()
        {
            
        }

        //每帧更新逻辑，类似于Unity的Update函数
        public override void OnUpdate()
        {
            
        }

        //计时器逻辑
        public override void OnTimer()
        {
            
        }

        //资源释放
        public override void Release()
        {
            
        }
        
        //设置按钮状态
        //0:未激活   1:已激活，当天未领取   2、已激活，当天已领取
        private void SetBtnStatus(int status)
        {
            m_goStatus0.SetActive(true);
            m_goStatus1.SetActive(status == 1);
            m_goStatus2.SetActive(status == 2);
            LayoutRebuilder.ForceRebuildLayoutImmediate(m_goBtnGroup);
        }

        //界面配置信息——不随着数据更新可能会改变，初始化一次即可
        private void InitPageConfigView()
        {
            var id = Manager.GetMonthlyCardConfigId();
            if (id == 0) return;

            if (!GameEntry.LDLTable.HaseTable<shopping_monthycard_weeklycard>()) return;
            var data = GameEntry.LDLTable.GetTable<shopping_monthycard_weeklycard>();
            var config = data.FirstOrDefault(x => x.card_id == id);
            if (config == null) return;
            
            var PayId = config.payment_id;
            var paymentData = Manager.GetPaymentConfig(PayId);
            
            //折扣数
            m_txtDiscount.text = $"{config.cost_effectiveness}%";
            
            //购买可获得的钻石数
            m_imgDiamond.SetImage(ToolScriptExtend.GetItemIcon(itemid.itemid_6));
            m_txtDiamondCount.text = $"x{paymentData.diamond}";

            //立即获得的奖励
            ToolScriptExtend.RecycleOrCreate(m_goRewardItem,m_transNowGet,config.card_reward_show.Count);
            ToolScriptExtend.ShowRewardList(m_transNowGet, config.card_reward_show,null,0.55f,1.3f);
            
            //每日领取的奖励
            ToolScriptExtend.RecycleOrCreate(m_goRewardItem,m_transDailyGet,config.card_daily_collection.Count);
            ToolScriptExtend.ShowRewardList(m_transDailyGet, config.card_daily_collection,null,0.55f,1.3f);
            
            //累充积分
            Manager.CreateRechargeScore(PayId,m_btnBuy.transform,40,-35);
            
            //购买按钮
            m_txtPrice.text = Manager.GetPrice(PayId);
            BindBtnLogic(m_btnBuy, () =>
            {
                GameEntry.PaymentData.Pay(PayId);
            });
            
            //领取按钮
            BindBtnLogic(m_btnGet, () =>
            {
                //领取当日月卡奖励
                Manager.C2SMonthlyCardDailyReceive((resp) =>
                {
                    Manager.RequestNewMsg(paymenttype.paymenttype_monthycard,InitPageView);
                });
            });

            //延长月卡
            m_txtAddTip.text = ToolScriptExtend.GetLang(1067);
            LayoutRebuilder.ForceRebuildLayoutImmediate(m_goDescRoot);
        }
        
        //界面信息——随着数据更新可能会改变
        private void InitPageView()
        {
            InitPageConfigView();
            var isActive = Manager.IsMonthlyCardActive();//月卡是否已激活
            
            //持续天数
            m_txtDayTip.text = ToolScriptExtend.GetLang(isActive ? 1100235 : 1100234);
            var remainDays = Manager.GetMonthlyCardDays().ToString();
            m_txtRemainDays.text = isActive ? remainDays : "30";
            
            //小队4等级
            var level = Manager.GetMonthlyCardLevel();
            m_txtLevel.text = $"{ToolScriptExtend.GetLang(1009)}4 Lv.{level}";
            
            //按钮组的状态
            //0:未激活   1:已激活，当天未领取   2、已激活，当天已领取
            var status = Manager.GetMonthlyCardStatus();
            SetBtnStatus(status);
            m_txtRemainCount.text = $"{ToolScriptExtend.GetLang(1066)}{remainDays}";
            
            m_txtRemainCount.gameObject.SetActive(status != 0);
            m_txtAddTip.gameObject.SetActive(status != 0);
        }
    }
}
