using System.Collections.Generic;
using System.Linq;
using Game.Hotfix.Config;
using Shop;
using UnityEngine;
using UnityEngine.UI;

namespace Game.Hotfix
{
    //周卡
    public class Mall_WeeklyCard:SwitchPanelLogic
    {
        public GameObject m_goPrefab;
        public UIText m_txtDiscount;
        public UIText m_txtDesc;
        public UIButton m_btnBuyAll;
        public UIText m_txtSumPriceTip;
        public UIText m_txtSumPrice;
        public UIButton m_btnFree;
        public GameObject m_goFreeOff;
        public GameObject m_goFreeOn;
        public GameObject m_goListRoot;
        public GameObject m_goGiftList;
        public UIButton m_btnGetAll;
        
        public GameObject middleEffect;
        public GameObject middleDiscountBg;
        
        private ScrollRect scrollRect;
        private Transform contentRoot;
        private List<ShopWeekCardGift> GiftList;
        private int sumCount = 0;
        private MallData Manager;

        private Dictionary<int, TimerNode> timeList;
        private List<int> recordList;
        private bool IsTimer = false;//是否开始倒计时逻辑
        private string descStr;
        private int mallFormDepth = 0;
        
        private class TimerNode
        {
            public int time;
            public UIText txt;
        }
        
        //实例化对象时初始化
        public override void OnInit()
        {
            base.OnInit();
            m_goPrefab.SetActive(false);
            IsTimer = false;
            descStr = ToolScriptExtend.GetLang(1066);//“剩余领取”
            Manager = GameEntry.LogicData.MallData;
            timeList = new Dictionary<int, TimerNode>();
            recordList = new List<int>();
            
            var discountBg = m_goGiftList.transform.Find("discountBg"); 
            var effect = m_goGiftList.transform.Find("effect"); 
            Manager.SetParticleDepth(effect.gameObject);
            Manager.SetParticleCanvasDepth(discountBg.gameObject,10);
            
            InitPageConfigView();
            scrollRect = m_goListRoot.GetComponent<ScrollRect>();
            contentRoot = scrollRect.content.transform;
            Manager.RequestNewMsg(paymenttype.paymenttype_weeklycard,InitPageView);

            var form = GameEntry.UI.GetUIForm(EnumUIForm.UIMallForm);
            if (form!=null)
            {
                mallFormDepth = form.Depth;
            }
            
            //领取所有
            BindBtnLogic(m_btnGetAll, () =>
            {
                Manager.C2SWeeklyCardReceivedDaily(0, (resp) =>
                {
                    Manager.RequestNewMsg(paymenttype.paymenttype_weeklycard,InitPageView);
                });
            });
            
            //领取免费礼包按钮
            BindBtnLogic(m_btnFree, () =>
            {
                var isFree = Manager.IsCanFree(paymenttype.paymenttype_weeklycard);
                if (!isFree) return;
                Manager.C2SWeeklyCardFreeReceive((resp) =>
                {
                    Manager.RequestNewMsg(paymenttype.paymenttype_weeklycard,InitPageView);
                });
            });
            
            Manager.SetParticleDepth(middleEffect);
            Manager.SetParticleCanvasDepth(middleDiscountBg,10);
        }
        
        //再次打开
        public override void OnReOpen()
        {
            InitPageConfigView();
            Manager.RequestNewMsg(paymenttype.paymenttype_weeklycard,InitPageView);
        }
        
        //本界面刷新，一般由UISwitchPage.RefreshCurPage触发
        public override void OnRefreshSelf()
        {
            base.OnRefreshSelf();
            Manager.RequestNewMsg(paymenttype.paymenttype_weeklycard,InitPageView);
        }
        
        //事件刷新
        public override void OnRefresh(object userData)
        {
            
        }

        /// <summary>
        /// 被选中触发的逻辑
        /// </summary>
        /// <param name="isOn">是否被选中</param>
        public override void OnSelect(bool isOn)
        {
            
        }
        
        //隐藏面板时逻辑
        public override void OnClose()
        {
            
        }

        //每帧更新逻辑，类似于Unity的Update函数
        public override void OnUpdate()
        {
            
        }

        //计时器逻辑
        public override void OnTimer()
        {
            if (IsTimer)
            {
                recordList.Clear();
                foreach (var item in timeList)
                {
                    var node = item.Value;
                    if (node.time > 0)
                    {
                        node.time--;
                        node.txt.text = $"{descStr}{ToolScriptExtend.FormatTime(node.time)}";
                    }
                    else
                    {
                        recordList.Add(item.Key);
                    }
                }

                foreach (var index in recordList)
                {
                    if (timeList.ContainsKey(index))
                    {
                        timeList.Remove(index);
                    }
                }
            }
        }

        //资源释放
        public override void Release()
        {
            GiftList?.Clear();
            timeList?.Clear();
            recordList?.Clear();
        }
        
        //界面配置信息——不随着数据更新可能会改变，初始化一次即可
        private void InitPageConfigView()
        {
            var data = GameEntry.LDLTable.GetTable<shopping_monthycard_weeklycard>();
            var config = data.FirstOrDefault(x => 10901001 == x.card_id);
            if (config == null) return;
            //合购折扣数值
            m_txtDiscount.text = $"{config.cost_effectiveness}%";
            var payId = config.payment_id;
            //购买按钮  价格  累充积分
            Manager.CreateRechargeScore(payId,m_btnBuyAll.transform,40,-35);
            BindBtnLogic(m_btnBuyAll, () =>
            {
                GameEntry.PaymentData.Pay(config.payment_id);
            });
            
            if (ToolScriptExtend.GetConfigById<payment>((int)payId, out var data1))
            {
                m_txtSumPrice.text = ToolScriptExtend.GetLang(data1.price_lang_id);
            }
        }
         
        //界面信息——随着数据更新可能会改变
        private void InitPageView()
        {
            var manager = Manager;
            GiftList = manager.GetWeeklyCardGiftSortList();
            sumCount = GiftList.Count;
            ToolScriptExtend.ClearAllChild(contentRoot);
            IsTimer = false;
            timeList.Clear();
            CreateGifts();
            //免费领取宝箱状态
            SetFreeRewardStatus();
            //合购按钮状态显示
            var sumStatus = Manager.GetWeeklyCardSumBtnStatus();
            m_btnBuyAll.gameObject.SetActive(sumStatus != 1);
            m_btnGetAll.gameObject.SetActive(sumStatus == 1);
            if (sumStatus == 0)
            {
                //全部购买
                m_txtSumPriceTip.text = ToolScriptExtend.GetLang(1100241);
            }
            else if (sumStatus == 2)
            {
                //全部续费
                m_txtSumPriceTip.text = ToolScriptExtend.GetLang(1100247);
            }
            
            CheckMultiLanguage(gameObject);
        }

        private void CreateGifts()
        {
            ToolScriptExtend.RecycleOrCreate(m_goGiftList,contentRoot,sumCount, (i, obj) =>
            {
                SetGiftView(i, obj);
                if (i == sumCount - 1)
                {
                    IsTimer = true;
                    var childCount = contentRoot.childCount;
                    if (childCount > 0)
                    {
                        for (var j = 0; j < childCount; j++)
                        {
                            var child = contentRoot.GetChild(j);
                            var effect = child.Find("effect");
                            effect.gameObject.SetActive(false);
                            effect.gameObject.SetActive(true);
                        }
                    }
                }
            });
        }
        
        private void SetGiftView(int index,GameObject obj)
        {
            if (index >= GiftList.Count && index < 0) return;
            
            var msg = GiftList[index];
            var data = GameEntry.LDLTable.GetTable<shopping_monthycard_weeklycard>();
            var configId = msg.Id;
            var config = data.FirstOrDefault(x => configId == x.card_id);
            if (config == null) return;
            
            var PayId = config.payment_id;
            var giftId = msg.Id;
            
            var root = obj.transform;
            var btn = root.Find("btn").GetComponent<UIButton>();//购买按钮
            var btnGet = root.Find("btnGet").GetComponent<UIButton>();//每天领奖按钮
            var price = root.Find("btn/price").GetComponent<UIText>();//价格
            price.text = Manager.GetPrice(PayId);
            
            //标题
            var name = root.Find("name").GetComponent<UIText>();//碎片数量
            name.text = ToolScriptExtend.GetLang(config.card_name);

            //购买按钮  价格  累充积分
            Manager.CreateRechargeScore(config.payment_id,btn.transform,40,-35);
            BindBtnLogic(btn, () =>
            {
                GameEntry.PaymentData.Pay(PayId);
            });
            
            //立即获得  货币图标  货币数量
            var nowCount = root.Find("nowCount").GetComponent<UIText>();
            var nowGetIcon = root.Find("nowGetIcon").GetComponent<UIImage>();
            // var nowGetData = config.card_reward.FirstOrDefault();
            if (ToolScriptExtend.GetConfigById<payment>((int)PayId, out var payData))
            {
                nowCount.text = ToolScriptExtend.FormatNumberWithUnit(payData.diamond);
            }
            nowGetIcon.SetImage(ToolScriptExtend.GetItemIcon(itemid.itemid_6));
            
            //每日领取  货币图标  货币数量
            var dailyCount = root.Find("dailyCount").GetComponent<UIText>();
            var dailyGetIcon = root.Find("dailyGetIcon").GetComponent<UIImage>();
            var dailyGetData = config.card_daily_collection.FirstOrDefault();
            GetBoxPropCount(dailyGetData, out var boxId, out var boxCount);
            dailyCount.text = ToolScriptExtend.FormatNumberWithUnit(boxCount);
            dailyGetIcon.SetImage(ToolScriptExtend.GetItemIcon(boxId));
            
            //总共可获得  货币图标  货币数量
            var sumCount = root.Find("sumCount").GetComponent<UIText>();
            var sumGetIcon = root.Find("sumGetIcon").GetComponent<UIImage>();
            sumCount.text = ToolScriptExtend.FormatNumberWithUnit(boxCount*config.card_duration);
            sumGetIcon.SetImage(ToolScriptExtend.GetItemIcon(boxId));
            
            //折扣
            var discount = root.Find("discountBg/discount").GetComponent<UIText>();//折扣
            discount.text = $"{config.cost_effectiveness}%";
            
            //状态 :0:未购买  1:已购买未领取  2:已购买已领取
            var dailyTip = root.Find("dailyTip");//今日已领取提示
            var status = Manager.GetWeeklyCardGiftStatus(giftId);
            btn.gameObject.SetActive(status != 1);
            dailyTip.gameObject.SetActive(status == 2);
            btnGet.gameObject.SetActive(status == 1);
            BindBtnLogic(btnGet, () =>
            {
                Manager.C2SWeeklyCardReceivedDaily(giftId,(resp) =>
                {
                    Manager.RequestNewMsg(paymenttype.paymenttype_weeklycard,InitPageView);
                });
            });
            
            //时间提示
            var time = root.Find("time").GetComponent<UIText>();
            if (status == 0)
            {
                //"有效期限x天"
                time.text = $"{ToolScriptExtend.GetLang(1100244)}{config.card_duration}{ToolScriptExtend.GetLang(1100094)}";
            }
            else
            {
                //剩余
                var value = msg.EndAt - TimeComponent.Now;
                var recordTime = value <= 0 ? 0 : (int)value;
                timeList.Add(giftId,new TimerNode(){time = recordTime,txt = time});
                time.text = ToolScriptExtend.FormatTime(recordTime);
            }
            
            var effect = root.Find("effect");
            effect.gameObject.SetActive(false);
            CheckMultiLanguage(obj.gameObject);
        }
        
        //设置周卡免费奖励领取状态
        private void SetFreeRewardStatus()
        {
            var isFree = Manager.IsCanFree(paymenttype.paymenttype_weeklycard);
            m_goFreeOff.SetActive(!isFree);
            m_goFreeOn.SetActive(isFree);
        }

        //每日领取奖励的计算
        private void GetBoxPropCount(reward rewardConfig,out itemid id,out int count)
        {
            var data = ToolScriptExtend.GetItemConfig(rewardConfig.item_id);
            id = 0;
            count = 0;
            if (data == null) return;
            var mainCity = GameEntry.LogicData.BuildingData.GetBuildingModuleById(101);
            var level = mainCity.LEVEL;
            if (data.item_subtype == itemsubtype.itemsubtype_useitemgetitem)
            {
                id = (itemid)int.Parse(data.use_value[0]);
                count = int.Parse(data.use_value[1])*(int)rewardConfig.num;
            }
            else if(data.item_subtype == itemsubtype.itemsubtype_levelchest)
            {
                //等级宝箱
                var searchId = (int)rewardConfig.item_id * 100 + level;
                if (ToolScriptExtend.GetConfigById<item_levelreward>(searchId, out var levelRewardConfig))
                {
                    var produce = levelRewardConfig.item_produce.FirstOrDefault();
                    id = produce.item_id;
                    count = (int)(produce.num*rewardConfig.num);
                }
            }
        }
    }
}