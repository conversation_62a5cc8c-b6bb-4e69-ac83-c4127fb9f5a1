using System.Collections.Generic;
using System.Linq;
using DG.Tweening;
using Game.Hotfix.Config;
using Mosframe;
using UnityEngine;

namespace Game.Hotfix
{
    //成长基金
    public class Mall_GrowthFund:SwitchPanelLogic
    {
        public UIText m_txtLevel;
        public UIButton m_btnBuy;
        public UIText m_txtPrice;
        public UIText m_txtDiscount;
        public UIButton m_btnAllGet;
        public TableViewV m_TableViewV;
        public GameObject m_goPrefab;
        public GameObject m_goListItem;
        public GameObject m_goLock;
        public GameObject m_goRewardItem;
        public UIButton m_btnTip;
        public UIButton m_btnSoldOut;
        public UIButton m_btnAllGetGrey;
        
        private MallData Manager;
        private int curLevel;
        private List<shopping_foundation> configList;
      
        //实例化对象时初始化
        public override void OnInit()
        {
            base.OnInit();
            m_goPrefab.SetActive(false);
            Manager = GameEntry.LogicData.MallData;
            configList = new List<shopping_foundation>();
            if (!GameEntry.LDLTable.HaseTable<shopping_foundation>())
            {
                return;
            }
            var data = GameEntry.LDLTable.GetTable<shopping_foundation>();
            if (data != null)
            {
                configList.AddRange(data);
            }
            CheckMultiLanguage(gameObject);
            Manager.RequestNewMsg(paymenttype.paymenttype_dawnfund, () =>
            {
                InitPageConfigView();
                InitPageView();
            });
            
            BindBtnLogic(m_btnAllGetGrey, () =>
            {
                //"目前没有可领取奖励"
                GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
                {
                    Content = ToolScriptExtend.GetLang(1100426)
                });
            });
        }
        
        //再次打开
        public override void OnReOpen()
        {
            Manager.RequestNewMsg(paymenttype.paymenttype_dawnfund, InitPageView);
        }
        
        //本界面刷新，一般由UISwitchPage.RefreshCurPage触发
        public override void OnRefreshSelf()
        {
            base.OnRefreshSelf();
            Manager.RequestNewMsg(paymenttype.paymenttype_dawnfund, InitPageView);
        }
        
        //事件刷新
        public override void OnRefresh(object userData)
        {
            
        }

        /// <summary>
        /// 被选中触发的逻辑
        /// </summary>
        /// <param name="isOn">是否被选中</param>
        public override void OnSelect(bool isOn)
        {
            
        }
        
        //隐藏面板时逻辑
        public override void OnClose()
        {
            
        }

        //每帧更新逻辑，类似于Unity的Update函数
        public override void OnUpdate()
        {
            
        }

        //计时器逻辑
        public override void OnTimer()
        {
            
        }

        //资源释放
        public override void Release()
        {
            
        }
        
        //界面配置信息——不随着数据更新可能会改变，初始化一次即可
        private void InitPageConfigView()
        {
            //合购性价比值
            m_txtDiscount.text = "";
            if (ToolScriptExtend.GetConfigById<shopping_settings>(107001, out var discountConfig))
            {
                m_txtDiscount.text = $"{discountConfig.value.FirstOrDefault()}%";
            }
            
            //购买按钮  价格  累充积分
            if (ToolScriptExtend.GetConfigById<shopping_settings>(107002, out var payConfig))
            {
                var payId = (paymentid)int.Parse(payConfig.value.FirstOrDefault());
                m_txtPrice.text =  Manager.GetPrice(payId);
                Manager.CreateRechargeScore(payId,m_btnBuy.transform);
                BindBtnLogic(m_btnBuy, () =>
                {
                    GameEntry.UI.OpenUIForm(EnumUIForm.UIGrowthFundBuyForm);
                });
            }
            
            //一键领取
            BindBtnLogic(m_btnAllGet, () =>
            {
                Manager.C2SDawnfundReceive((resp) =>
                {
                    Manager.RequestNewMsg(paymenttype.paymenttype_dawnfund, InitPageView);
                });
            });
            
            //说明按钮
            BindBtnLogic(m_btnTip, () =>
            {
                GameEntry.UI.OpenUIForm(EnumUIForm.UIComDescForm, 6);
            });
            
            m_TableViewV.GetItemCount = () => configList.Count;
            m_TableViewV.GetItemGo = () => m_goListItem;
            m_TableViewV.UpdateItemCell = UpdateItemLogic;
            m_TableViewV.InitTableViewByIndex(0);
            
        }
         
        //界面信息——随着数据更新可能会改变
        private void InitPageView()
        {
            //建筑等级
            var level = Manager.GetDawnfundLevel();
            curLevel = level;
            m_txtLevel.text = $"Lv.{level}";
            
            //重新加载列表
            if (m_TableViewV.itemPrototype == null)
            {
                m_TableViewV.InitTableViewByIndex(0);
            }
            else{
                m_TableViewV.ReloadData();
            }
            
            //合购按钮
            var isVipUnlock = Manager.IsDawnfundVipActive();
            m_btnBuy.gameObject.SetActive(!isVipUnlock);
            // m_btnSoldOut.gameObject.SetActive(isVipUnlock);
            m_btnSoldOut.gameObject.SetActive(false);
            m_goLock.gameObject.SetActive(!isVipUnlock);
            
            var canGet = Manager.IsCanGetDawnFundReward();
            m_btnAllGet.gameObject.SetActive(canGet);
            m_btnAllGetGrey.gameObject.SetActive(!canGet);

            ScrollToRewardIndex();
        }
        
        protected void UpdateItemLogic(int index, GameObject obj)
        {
            if (index >= configList.Count && index < 0) return;
            
            var config = configList[index];
            var root = obj.transform;
            
            // var btn = root.Find("btn").GetComponent<UIButton>();//购买按钮
            
            //免费奖励
            var freeNode = root.Find("freeNode");
            var freeRewardConfig = config.reward_free.FirstOrDefault();
            SetReward(index,freeNode, freeRewardConfig.item_id, (int)freeRewardConfig.num,false);
            
            //等级数值
            var level = root.Find("level").GetComponent<UIText>();
            level.text = config.foundation_target.ToString();
            
            //购买奖励
            var vipNode1 = root.Find("vipNode1");
            var vipNode2 = root.Find("vipNode2");
            var vipRewardConfig1 = config.reward_paid[0];
            var vipRewardConfig2 = config.reward_paid[1];
            SetReward(index,vipNode1, vipRewardConfig1.item_id, (int)vipRewardConfig1.num,true);
            SetReward(index,vipNode2, vipRewardConfig2.item_id, (int)vipRewardConfig2.num,true);
            
            var style1 = root.Find("style1");
            var style2 = root.Find("style2");
            var isFinal = index == configList.Count-1;
            style1.gameObject.SetActive(!isFinal);
            style2.gameObject.SetActive(isFinal);
            
            var line = root.Find("line");
            line.gameObject.SetActive(!isFinal);
            var up = root.Find("up");
            var down = root.Find("down");
            
            up.gameObject.SetActive(config.foundation_target <= curLevel);
            down.gameObject.SetActive(!isFinal && config.foundation_target <= curLevel);
            
            var blue = root.Find("blue");
            blue.gameObject.SetActive(config.foundation_target <= curLevel);
        }
        
        public void SetReward(int index,Transform root,itemid id,int count,bool isVip)
        {
            var childCount1 = root.childCount;
            var config = configList[index];
            GameObject rewardObj = null;
            rewardObj = childCount1 == 0 ? Instantiate(m_goRewardItem,root) : root.GetChild(0).gameObject;
            var node = rewardObj.transform.Find("node");
            var effect = rewardObj.transform.Find("effect");
            var received = rewardObj.transform.Find("received");
            var lockObj = rewardObj.transform.Find("lock");
            var btn = rewardObj.transform.Find("btn").GetComponent<UIButton>();
            
            Manager.SetParticleDepth(effect.gameObject);
            
            var info = new reward() { item_id = id, num = count };
            ToolScriptExtend.LoadReward(node,info, null, 0.6f,1.5f);
            
            //奖励状态显示  0:未解锁  1：已解锁未领取  2：已解锁已领取
            var status = Manager.GetGrowthFundRewardStatus(config.foundation_target,isVip);
            lockObj.gameObject.SetActive(status == 0);
            received.gameObject.SetActive(status == 2);
            if (isVip)
            {
                effect.gameObject.SetActive(status != 2);
            }
            else
            {
                effect.gameObject.SetActive(status == 1);
            }
            
            btn.gameObject.SetActive(status != 2);
            BindBtnLogic(btn, () =>
            {
                if(status == 0)
                {
                    if (isVip)
                    {
                        var isFinish = config.foundation_target <= curLevel;
                        if (isFinish)
                        {
                            //弹出购买窗口
                            GameEntry.UI.OpenUIForm(EnumUIForm.UIGrowthFundBuyForm);
                        }
                        else
                        {
                            var module = node.GetChild(0).GetComponent<UIItemModule>();
                            module.OpenTips();
                        }
                    }
                    else
                    {
                        var module = node.GetChild(0).GetComponent<UIItemModule>();
                        module.OpenTips();
                    }
                }
                else if (status == 1)
                {
                    //领取奖励
                    Manager.C2SDawnfundReceive((resp) =>
                    {
                        Manager.RequestNewMsg(paymenttype.paymenttype_dawnfund, InitPageView);
                    });
                }
            });
        }
        
        
        //列表滑动到第一个可领取奖励的位置上,或者进行中的节点item
        private void ScrollToRewardIndex()
        {
            if (configList == null || configList.Count == 0) return;

            var sortList = new List<shopping_foundation>();
            sortList.AddRange(configList);
            
            sortList.Sort((a, b) =>
            {
                var statusA = CheckItemStatus(a);
                var statusB = CheckItemStatus(b);
                var flagA = GetCompareFlag(statusA);
                var flagB = GetCompareFlag(statusB);
                if (flagA != flagB) return flagA - flagB;
                return a.id - b.id;
            });
            
            var result = sortList[0];
            var resultScore = result.id;
            var resultStatus = CheckItemStatus(result);
            var index = configList.FindIndex(x => x.id == resultScore);
            if (resultStatus == 0)
            {
                if (index - 1 < 0)
                {
                    index = 0;
                }
                else
                {
                    index--;
                }
            }
            
            DOVirtual.DelayedCall(0.2f, () =>
            {
                 m_TableViewV.MoveToTargetIndex(index,0.3f);
            });
        }
        
        //奖励状态显示  0:未解锁  1：免费奖励或vip奖励可领取  2、免费奖励已领取,vip奖励未解锁  3：都已领取
        private int CheckItemStatus(shopping_foundation config)
        {
            //0:未解锁  1：已解锁未领取  2：已解锁已领取
            var freeStatus = Manager.GetGrowthFundRewardStatus(config.foundation_target,false);
            if (freeStatus == 0) return 0;
            
            var vipStatus = Manager.GetGrowthFundRewardStatus(config.foundation_target,true);

            if (freeStatus == 1 || vipStatus == 1)
            {
                return 1;
            }
            
            if (freeStatus == 2 && vipStatus == 0)
            {
                return 2;
            }
            
            if (freeStatus == 2 && vipStatus == 2)
            {
                return 3;
            }

            return 0;
        }
        
        // 免费奖励或vip奖励可领取(1)>>未解锁(2)>>免费奖励已领取,vip奖励未解锁(3)>>都已领取(4)
        private int GetCompareFlag(int status)
        {
            var flag = 4;
            //status  0:未解锁  1：免费奖励或vip奖励可领取  2、免费奖励已领取,vip奖励未解锁  3：都已领取
            if (status == 0)
            {
                flag = 2;
            }
            else if (status == 1)
            {
                flag = 1;
            }
            else if (status == 2)
            {
                flag = 3;
            }
            else if (status == 3)
            {
                flag = 4;
            }
            return flag;
        }
    }
}
