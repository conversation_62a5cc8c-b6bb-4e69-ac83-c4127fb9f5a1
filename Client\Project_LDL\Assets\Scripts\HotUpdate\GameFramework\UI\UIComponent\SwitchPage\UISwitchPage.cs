using System;
using System.Collections.Generic;
using System.Linq;
using Codice.CM.Common.Checkin.Partial;
using UnityEngine;
using UnityEngine.Events;

namespace Game.Hotfix
{
    public class UISwitchPage : MonoBehaviour
    {
        private Transform panelRoot;
        private UISwitchTagGroup switchTagGroup;
        public UISwitchTagGroup SwitchTagGroup => switchTagGroup;

        private UnityAction<int> callback;
        
        public EnumUIForm EnumUIForm { set; get; }
        private class PanelNode
        {
            public int uniqueId;//唯一id
            public string name;
            public string objPath;
        }

        //缓存已加载过的面板
        private Dictionary<int, SwitchPanelLogic> panelCache;
        private List<PanelNode> panelDataList;
        private Dictionary<int, object> bindDataList = new Dictionary<int, object>();//绑定数据列表

        private Dictionary<int, long> timerRecord;//记录界面查看时的时间，超过一定时间未再次查看时，需要将其删除 
        private List<int> deleteList;
        [HideInInspector]
        public bool UseTimer = false;//是否开启定时器逻辑
        
        #region 外部调用接口
        /// <summary>
        /// 组件初始化
        /// </summary>
        /// <param name="tagObj">标签预制体</param>
        /// <param name="tagRoot">标签实例化父节点</param>
        /// <param name="_panelRoot">子界面实例化父节点</param>
        /// <param name="pageList">将标签名和界面预制体名用|分割符拼接传入</param>
        public void OnInit(GameObject tagObj,
            Transform tagRoot, 
            Transform _panelRoot,
            List<ValueTuple<int,string, string>> pageList,
            UnityAction<int> _callback = null)
        {
            Release();
            panelRoot = _panelRoot;
            switchTagGroup = GetComponentInChildren<UISwitchTagGroup>();
            panelDataList = TurnToNodeList(pageList);
            panelCache = new Dictionary<int, SwitchPanelLogic>();
            timerRecord = new Dictionary<int, long>();
            deleteList = new List<int>();
            callback += _callback;
            
            var nameList = panelDataList.Select(x => x.name).ToList();
            var idList = panelDataList.Select(x => x.uniqueId).ToList();
            
            switchTagGroup.Init(tagObj,tagRoot,nameList, OnSwitchPage);
            switchTagGroup.BindUniqueId(idList);
            
            BindTimerLogic();
        }

        //选择激活指定索引（从0开始）的界面
        public void SelectPageByIndex(int index)
        {
            switchTagGroup.OnSelectLogic(index);
        }
        
        //释放资源
        public void Release()
        {   
            Timers.Instance.Remove(GetInstanceID().ToString());
            if (switchTagGroup != null)
            {
                switchTagGroup.Release();
            }
            if (panelCache != null)
            {
                foreach (var cache in panelCache)
                {
                    cache.Value.Release();
                }
            }
            callback = null;
            panelCache?.Clear();
            panelDataList?.Clear();
            timerRecord?.Clear();
            deleteList?.Clear();
            if (panelRoot != null)
            {
                ToolScriptExtend.ClearAllChild(panelRoot);
            }
        }

        //获取当前展示的界面索引
        public int GetCurPageIndex()
        {
            return switchTagGroup.CurSelectIndex;
        }
        
        //获取当前展示的界面绑定的唯一id
        public int GetCurUniqueId()
        {
            return switchTagGroup.CurSelectUniqueId;
        }

        //刷新逻辑
        public void OnRefresh(object userData)
        {
            foreach (var cache in panelCache)
            {
                cache.Value.OnRefresh(userData);
            }

            // if (panelRoot.childCount > 0)
            // {
            //     var child = panelRoot.GetChild(0);
            //     var script = child.GetComponent<SwitchPanelLogic>();
            //     if (script != null)
            //     {
            //         script.OnRefresh(userData);
            //     }
            // }
        }

        //触发指定索引的界面的更新函数
        public void RefreshCurPage()
        {
            RefreshPageByIndex(switchTagGroup.CurSelectIndex);
        }
        
        //触发指定索引的界面的更新函数
        public void RefreshPageByIndex(int index)
        {
            if (panelCache.TryGetValue(index, out var panel))
            {
                panel.OnRefreshSelf();
            }
        }

        public SwitchPanelLogic GetSwitchPanelLogic(int index)
        {
            if (panelCache.TryGetValue(index, out var panel))
            {
                return panel;
            }
            return null;
        }
        
        /// <summary>
        /// 给指定界面绑定数据，类似于传入数据
        /// </summary>
        /// <param name="index"></param>
        /// <param name="data"></param>
        public void BindDataByIndex(int index, object data)
        {
            bindDataList[index] = data;
        }
        
        /// <summary>
        /// 给指定界面绑定数据，类似于传入数据
        /// </summary>
        /// <param name="index"></param>
        /// <param name="data"></param>
        public object GetBindDataByIndex(int index)
        {
            if (bindDataList.TryGetValue(index, out var data))
            {
                return data;
            }
            return null;
        }
        #endregion
        
        #region 内部逻辑

        private List<PanelNode> TurnToNodeList(List<ValueTuple<int,string, string>> pageList)
        {
            var list = new List<PanelNode>();
            foreach (var node in pageList)
            {
                list.Add(new PanelNode() { uniqueId = node.Item1,name = node.Item2, objPath = node.Item3 });
            }
            return list;
        }

        private void OnSwitchPage(int index)
        {
            // ToolScriptExtend.ClearAllChild(panelRoot);
            //
            // var data = panelDataList[index];
            // ToolScriptExtend.LoadPrefab($"UI/UIChildForm/{data.objPath}",(prefab) =>
            // {
            //     var obj = Instantiate(prefab, panelRoot);
            //     obj.name = obj.name.Replace("(Clone)", ""); 
            //     var script = obj.GetComponent<SwitchPanelLogic>();
            //     script.panelIndex = index;
            //     script.switchPage = this;
            //     script.OnInit();
            //     // panelCache.Add(index, script);
            //     // ShowOnePanel(index);
            // });
            
            if (panelCache.ContainsKey(index))
            {
                ShowOnePanel(index);
            }
            else
            {
                var data = panelDataList[index];
                ToolScriptExtend.LoadPrefab($"UI/UIChildForm/{data.objPath}",(prefab) =>
                {
                    var obj = Instantiate(prefab, panelRoot);
                    var script = obj.GetComponent<SwitchPanelLogic>();
                    script.panelIndex = index;
                    script.switchPage = this;
                    script.OnInit();
                    panelCache.Add(index, script);
                    ShowOnePanel(index);
                });
            }
            
            if (panelCache.TryGetValue(index, out var panel))
            {
                panel.OnReOpen();
            }
            callback?.Invoke(index);
        }

        //销毁许久未打开的历史界面
        //删除超过5秒未再打开过的界面
        //如果加载的界面超过五个，就删除最早远打开的界面
        private void DestroyHistoryPanel()
        {
            deleteList.Clear();
            var preRemoveCount = panelCache.Count - 3;
            if (preRemoveCount >= 0)
            {
                while (preRemoveCount > 0)
                {
                    var index = GetOldestIndex();
                    if (index != -1)
                    {
                        if (panelCache.TryGetValue(index, out var data))
                        {
                            deleteList.Add(index);
                        }
                    }
                    preRemoveCount--;
                }
            }
            
            foreach (var node in timerRecord)
            {
                var offset = TimeComponent.Now - node.Value;
                if (offset > 10)
                {
                    if (!deleteList.Contains(node.Key))
                    {
                        if (panelCache.TryGetValue(node.Key, out var data))
                        {
                            deleteList.Add(node.Key);
                        }
                    }
                }
            }
            
            if (deleteList.Count > 0)
            {
                foreach (var key in deleteList)
                {
                    if (panelCache.TryGetValue(key, out var data))
                    {
                        if(data == null)continue;
                        panelCache.Remove(key);
                        data.OnClose();
                        data.Release();
                        DestroyImmediate(data.gameObject);
                    }

                    if (timerRecord.ContainsKey(key))
                    {
                        timerRecord.Remove(key);
                    }
                }
            }
        }
        
        //查找最早远打开的界面索引
        private int GetOldestIndex()
        {
            var index = -1;
            long record = -1;
            foreach (var node in timerRecord)
            {
                if (record == -1)
                {
                    record = node.Value;
                    index = node.Key;
                }
                else
                {
                    if (record > node.Value)
                    {
                        record = node.Value;
                        index = node.Key;
                    }
                }
            }
            return index;
        }
        
        //显示选中面板，隐藏未选中面板
        private void ShowOnePanel(int index)
        {
            foreach (var cache in panelCache)
            {
                if(cache.Value == null)continue;
                
                var isSelect = cache.Key == index;
                if (isSelect)
                {
                    timerRecord[cache.Key] = TimeComponent.Now;
                }
                else
                {
                    if (cache.Value.gameObject.activeInHierarchy)
                    {
                        timerRecord[cache.Key] = TimeComponent.Now;
                    }
                }
                cache.Value.SetActive(isSelect);
                if (!isSelect)
                {
                    cache.Value.OnClose();
                }
            }
            DestroyHistoryPanel();
        }
        
        //定时器逻辑
        private void BindTimerLogic()
        {
            //开启一个定时器
            Timers.Instance.Add(GetInstanceID().ToString(), 1, (a) =>
            {
                if (UseTimer)
                {
                    foreach (var cache in panelCache)
                    {
                        if (cache.Value != null)
                        {
                            cache.Value.OnTimer();
                        }
                    }
                }
            },86400);
        }

        //触发所有子界面的OnClose函数
        public void CloseAllPanelLogic()
        {
            foreach (var panel in panelCache.Values)
            {
                if (panel != null)
                {
                    panel.OnClose();
                }
            }
        }
        
        private void OnDestroy()
        {
            Release();
        }

        #endregion
    }
}