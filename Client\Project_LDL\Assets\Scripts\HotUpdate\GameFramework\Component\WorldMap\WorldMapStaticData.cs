using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using GameFramework;
using Sirenix.OdinInspector;
using Sirenix.Utilities;
using UnityEditor;
using UnityEngine;
using Random = System.Random;

namespace Game.Hotfix
{
    #region tileData 结构

    [System.Serializable]
    public class TileData
    {
        public int compressionlevel;
        public int height;
        public bool infinite;
        public List<LayerTM> layers; // Renamed Layer to LayerTM to avoid conflict if you have a Unity.Layer
        public int nextlayerid;
        public int nextobjectid;
        public string orientation;
        public string renderorder;
        public string tiledversion;
        public int tileheight;
        public List<TilesetTM> tilesets; // Renamed Tileset to TilesetTM
        public int tilewidth;
        public string type;
        public string version;
        public int width;
    }

    [System.Serializable]
    public class LayerTM // Renamed to avoid conflict with Unity's Layer class
    {
        public string draworder; // Specific to object group layers
        public int id;
        public bool locked;
        public string name;
        public List<MapObjectTM> objects; // Specific to object group layers
        public float opacity; // Using float for 0.5
        public string type; // "objectgroup" or "tilelayer"
        public bool visible;
        public int x;
        public int y;

        // Properties specific to tile layers
        // JsonUtility will assign default values (0 for int, null for List) if not present in JSON
        public List<int> data;
        public int height; // This is layer height (for tile layers)
        public int width; // This is layer width (for tile layers)
    }

    [System.Serializable]
    public class MapObjectTM // Renamed to avoid any potential conflicts
    {
        public int gid;
        public float height; // Using float for potential floating point values
        public int id;
        public string name;
        public float rotation;
        public string type;
        public bool visible;
        public float width; // Using float due to 20001.333...
        public float x;
        public float y;
    }

    [System.Serializable]
    public class TilesetTM // Renamed to avoid any potential conflicts
    {
        public int firstgid;
        public string source;
    }
    #endregion


    [CreateAssetMenu(fileName = "WorldMapData", menuName = "WorldMap/地图数据", order = 1)]
    public class WorldMapStaticData : SerializedScriptableObject
    {
        public ushort[] areaData;

        [SerializeField] public Dictionary<Vector2Int, List<Vector3Int>> TreeDic;
        [SerializeField] public Dictionary<Vector2Int, List<Vector3Int>> ObstacleDic;
        

#if UNITY_EDITOR
        [LabelText("数据层名称")] public string LayersDataName = "";
        [LabelText("植被层名称")] public string LayerTreeName = "";
        [LabelText("障碍层名称")] public string LayerObstacleName = "";

        [LabelText("数据原始文件")]
        [Sirenix.OdinInspector.FilePath(Extensions = "tmj", ParentFolder = "Assets/Editor/WroldMap/Tiled")]
        public string FileName = "";
        
        [LabelText("区域边框生成目录")]
        [Sirenix.OdinInspector.FolderPath]
        public string AreaBorderGenPath = "";

        [LabelText("区域边框模板")]
        public GameObject AreaBorderTemplate;
        
        [Sirenix.OdinInspector.Button("Test")]
        public void Test()
        {
            TreeDic = new Dictionary<Vector2Int, List<Vector3Int>>();
            List<Vector3Int> list = new List<Vector3Int>();
            Vector3Int d = new Vector3Int(3, 2, 55);
            list.Add(d);
            TreeDic.Add(new Vector2Int(7, 9), list);

            UnityEditor.EditorUtility.SetDirty(this);
            UnityEditor.AssetDatabase.SaveAssetIfDirty(this);
        }

        [Sirenix.OdinInspector.Button("Read")]
        public void Read()
        {
            if (string.IsNullOrEmpty(FileName))
            {
                Debug.LogError("未指定文件");
                return;
            }

            var text = FileUtility.SafeReadAllText("Assets/Editor/WroldMap/Tiled/" + FileName);
            if (string.IsNullOrEmpty(text))
            {
                Debug.LogError("数据为空");
                return;
            }

            Debug.LogError(text.Length);


            var data = JsonUtility.FromJson<TileData>(text);
            if (data.layers != null)
            {
                foreach (var layer in data.layers)
                {
                    if (layer.name == LayersDataName)
                    {
                        ParseMapData(data, layer);
                    }
                    else if (layer.name == LayerTreeName)
                    {
                        ParseTreeData(data, layer);
                    }
                    else if (layer.name == LayerObstacleName)
                    {
                        ParseObstacleData(data, layer);
                    }
                }
            }


            Debug.LogError("Read end");
            UnityEditor.EditorUtility.SetDirty(this);
            UnityEditor.AssetDatabase.SaveAssetIfDirty(this);
        }

        private void ParseMapData(TileData tileData, LayerTM layerTM)
        {
            var width = tileData.width;
            var height = tileData.height;
            ushort[,] tempAreaData = new ushort[width, height];

            //坐标转化
            for (int i = 0; i < layerTM.data.Count; i++)
            {
                int tileGid = layerTM.data[i];
                int y = i / height;//(height - 1) - i / height; // Row index 反转Y
                int x = i % width; // Column index
                tempAreaData[x, y] = (ushort)tileGid;
            }

            //写入
            areaData = new ushort[tempAreaData.GetLength(0) * tempAreaData.GetLength(1)];
            for (int y = 0; y < height; y++) // 遍历每一行
            {
                for (int x = 0; x < width; x++) // 遍历当前行中的每一列
                {
                    // 计算在一维数组中的索引
                    // 对于行优先，索引 = (当前行号 * 每行的元素个数) + 当前列号
                    int indexIn1DArray = y * width + x;
                    areaData[indexIn1DArray] = tempAreaData[x, y];
                }
            }
            
            //查找边界
            Dictionary<int, List<Vector2Int>> areaBorderComplete = new Dictionary<int, List<Vector2Int>>();
            for (int y = 0; y < height; y++) // 遍历每一行
            {
                for (int x = 0; x < width; x++) // 遍历当前行中的每一列
                {
                    var areaId = tempAreaData[x, y];
                    Vector2Int point = new Vector2Int(x, y);
                    if (!areaBorderComplete.ContainsKey(areaId) && IsBorder(tempAreaData,areaId,point))
                    {
                        var list = GetAreaBorder(tempAreaData, areaId, point, width, height);
                        areaBorderComplete.Add(areaId, list);
                    }
                }
            }
            
            //创建预设体 
            string prefabName = "areaBorder_allInOne";
            GameObject emptyGO = new GameObject(prefabName);
            AreaBorderDataHolder dataHolder = emptyGO.AddComponent<AreaBorderDataHolder>();
            dataHolder.AreaData = new Dictionary<int, GameObject>();
            foreach (var pathData in areaBorderComplete)
            {
                var areaBorder = Instantiate(AreaBorderTemplate, emptyGO.transform);
                var lineRenderer = areaBorder.GetComponent<LineRenderer>();
                lineRenderer.gameObject.GetOrAddComponent<MaterialColorSetter>();

                //保留所有拐点
                var cornnerPoints = MapGridUtils.GetCornerPoints(pathData.Value);
                // var cornnerPoints = pathData.Value;

                Vector3[] positions = new Vector3[cornnerPoints.Count];
                for (int i = 0; i < cornnerPoints.Count; i++)
                {
                    var curPoint = cornnerPoints[i];
                    Vector2 offset = Vector2.zero;

                    offset = ResetOffsetToBorder(tempAreaData, curPoint);

                    positions[i] = new Vector3(curPoint.x + 0.5f + offset.x, 0.1f, curPoint.y + 0.5f + offset.y);
                }
                
                lineRenderer.positionCount = positions.Length;
                lineRenderer.SetPositions(positions);

                dataHolder.AreaData.TryAdd(pathData.Key, areaBorder);
            }
            
            var fullPath = Path.Combine(AreaBorderGenPath, prefabName + ".prefab");
            PrefabUtility.SaveAsPrefabAsset(emptyGO, fullPath);
            GameObject.DestroyImmediate(emptyGO);
            
            
            //拆分边界
            // ushort gridSize = 100;
            // AreaBorder = new Dictionary<Vector2Int, List<WorldMapAreaPathData>>();
            // foreach (var item in areaBorderComplete)
            // {
            //     int areaId = item.Key;
            //     for (int i = 0; i < item.Value.Count; i++)
            //     {
            //         Vector2Int point = item.Value[i];
            //         var gridId = MapGridUtils.GetGrid(point.x, point.y, gridSize);
            //         
            //         if (!AreaBorder.ContainsKey(gridId))
            //         {
            //             List<WorldMapAreaPathData> list = new List<WorldMapAreaPathData>();
            //             AreaBorder.Add(gridId, list);
            //         }
            //
            //         if (AreaBorder.TryGetValue(gridId, out var pathList))
            //         {
            //             bool hasAdd = false;
            //             foreach (var p in pathList.Where(p => p.areaId == areaId))
            //             {
            //                 p.Add(point);
            //                 hasAdd = true;
            //             }
            //
            //             if (!hasAdd)
            //             {
            //                 WorldMapAreaPathData worldMapAreaPathData = new WorldMapAreaPathData(areaId);
            //                 worldMapAreaPathData.Add(point); 
            //                 pathList.Add(worldMapAreaPathData);
            //             }
            //         }
            //     }
            // }
            //创建预设体
            //先清空
            // string[] prefabPaths = Directory.GetFiles(AreaBorderGenPath, "*.prefab", SearchOption.TopDirectoryOnly);
            // foreach (string prefabPath in prefabPaths)
            // {
            //     bool success = AssetDatabase.DeleteAsset(prefabPath);
            //     if (success)
            //         Debug.Log("Deleted prefab: " + prefabPath);
            //     else
            //         Debug.LogError("Failed to delete prefab: " + prefabPath);
            // }
            // AssetDatabase.Refresh();
            // //再创建
            // foreach (var item in AreaBorder)
            // {
            //     string prefabName = "areaBorder_" + item.Key.x + "_" + item.Key.y;
            //     GameObject emptyGO = new GameObject("prefabName");
            //
            //     foreach (var pathData in item.Value)
            //     {
            //         var areaBorder = GameObject.Instantiate(AreaBorderTemplate, emptyGO.transform);
            //         var lineRenderer = areaBorder.GetComponent<LineRenderer>();
            //         var positions = pathData.GetPositions();
            //         lineRenderer.positionCount = positions.Length;
            //         lineRenderer.SetPositions(positions);
            //     }
            //     
            //     var fullPath = Path.Combine(AreaBorderGenPath, prefabName + ".prefab");
            //     PrefabUtility.SaveAsPrefabAsset(emptyGO, fullPath);
            //     GameObject.DestroyImmediate(emptyGO);
            // }
            // AssetDatabase.Refresh();
            Debug.LogError("DD");
        }

        private Vector2 ResetOffsetToBorder(ushort[,] data,Vector2Int curPoint)
        {
            float moveConst = 0.4f;
            
            List<Vector2Int> list = new List<Vector2Int>();
            list.Add(new Vector2Int(-1, 1));
            list.Add(new Vector2Int(-1, -1));
            list.Add(new Vector2Int(1, 1));
            list.Add(new Vector2Int(1, -1));

            list.Add(new Vector2Int(0, 1));
            list.Add(new Vector2Int(0, -1));
            list.Add(new Vector2Int(1, 0));
            list.Add(new Vector2Int(-1, 0));

            Vector2Int res = Vector2Int.zero;
            for (int i = 0; i < list.Count; i++)
            {
                var checkX = curPoint.x + list[i].x;
                var checkY = curPoint.y + list[i].y;
                if (checkX < 0 || checkX >= data.GetLength(0) || checkY < 0 || checkY >= data.GetLength(1))
                    res += list[i];
                else if (data[checkX, checkY] != data[curPoint.x, curPoint.y])
                    res += list[i];

            }

            Vector2 res2 = Vector2.zero;
            if (Mathf.Abs(res.x) != 0)
                res2.x = Mathf.Sign(res.x) * moveConst;
            if(Mathf.Abs(res.y) != 0)
                res2.y = Mathf.Sign(res.y) * moveConst;
            return res2;

        }
        
        // private float ResetOffsetToBorderX(ushort[,] data,Vector2Int curPoint,int value,float moveConst = 0.25f)
        // {
        //     if(curPoint.x + value < 0 || curPoint.x + value >= data.GetLength(0))
        //         return 0;
        //
        //     if (data[curPoint.x, curPoint.y] != data[curPoint.x + value, curPoint.y])
        //         return 0 + moveConst * value;
        //     return 0;
        // }
        //
        // private float ResetOffsetToBorderY(ushort[,] data,Vector2Int curPoint,int value,float moveConst = 0.25f)
        // {
        //     if(curPoint.y + value < 0 || curPoint.y + value >= data.GetLength(1))
        //         return 0;
        //
        //     if (data[curPoint.x, curPoint.y] != data[curPoint.x, curPoint.y + value])
        //         return moveConst * value;
        //     return 0;
        // }
        
        private void ParseTreeData(TileData tileData, LayerTM layerTM)
        {
            var width = tileData.width;
            var height = tileData.height;

            ushort gridSize = 20;

            TreeDic = new Dictionary<Vector2Int, List<Vector3Int>>();

            for (int i = 0; i < layerTM.data.Count; i++)
            {
                if (layerTM.data[i] != 0)
                {
                    int y = i / height;//(height - 1) - i / height; // Row index 反转Y
                    int x = i % width; // Column index

                    var key = MapGridUtils.GetGrid(x, y, gridSize);
                    if (!TreeDic.ContainsKey(key))
                    {
                        TreeDic.Add(key, new List<Vector3Int>());
                    }

                    if (TreeDic.TryGetValue(key, out var list))
                    {
                        list.Add(new Vector3Int(x, y, GetRandomTreeId()));
                    }
                }
            }
        }

        private void ParseObstacleData(TileData tileData, LayerTM layerTM)
        {
            var width = tileData.width;
            var height = tileData.height;
            
            Vector2Int gridSize = GameDefine.WorldMapDataGridSize;
            
            ObstacleDic = new Dictionary<Vector2Int, List<Vector3Int>>();
            
            for (int i = 0; i < layerTM.data.Count; i++)
            {
                if (layerTM.data[i] != 0)
                {
                    int y = i / height;//(height - 1) - i / height; // Row index 反转Y
                    int x = i % width; // Column index

                    var key = MapGridUtils.GetGrid(x, y, gridSize);
                    if (!ObstacleDic.ContainsKey(key))
                    {
                        ObstacleDic.Add(key, new List<Vector3Int>());
                    }

                    if (ObstacleDic.TryGetValue(key, out var list))
                    {
                        list.Add(new Vector3Int(x, y, layerTM.data[i]));
                    }
                }
            }
            
        }

        private int GetRandomTreeId()
        {
            return Utility.Random.GetRandom(1, 50);
        }

        #region 查找边界
        public static List<Vector2Int> GetAreaBorder(ushort[,] tempAreaData,int areaId,Vector2Int startPoint, int gridLength, int gridWidth)
        {
            HashSet<Vector2Int> visited = new HashSet<Vector2Int>();

            List<Vector2Int> blueCoordinates = new List<Vector2Int>();
            Stack<Vector2Int> stack = new Stack<Vector2Int>();
            // 初始化
            stack.Push(startPoint);
            visited.Add(startPoint); // 标记起点为已访问

            List<Vector2Int> GetUnvisitedNeighbors(Vector2Int point)
            {
                List<Vector2Int> neighbors = new List<Vector2Int>();

                // 定义四个方向：上、下、左、右
                int[] dx = { 0, 0, -1, 1 };
                int[] dy = { -1, 1, 0, 0 };

                // 遍历四个方向
                for (int i = 0; i < dx.Length; i++)
                {
                    int newX = point.x + dx[i];
                    int newY = point.y + dy[i];

                    // 检查是否越界
                    if (newX >= 0 && newX < gridLength && newY >= 0 && newY < gridWidth)
                    {
                        Vector2Int neighbor = new Vector2Int(newX, newY);
                        //检查是否是蓝色格子，并且没有被访问过
                        if (tempAreaData[newX,newY] == areaId && IsBorder(tempAreaData,areaId,neighbor) && !visited.Contains(neighbor))
                        {
                            neighbors.Add(neighbor);
                        }
                    }
                }

                return neighbors;
            }
            
            // 开始广度优先搜索
            while (stack.Count > 0)
            {
                Vector2Int current = stack.Pop();
                blueCoordinates.Add(current);

                // 获取周围未访问过的可用格子
                List<Vector2Int> neighbors = GetUnvisitedNeighbors(current);

                neighbors.Sort((a,b) =>
                {
                    if (GetPriority(tempAreaData, areaId, a) > GetPriority(tempAreaData, areaId, b))
                        return 1;
                    return -1;
                });
                
                // 将邻居加入队列，并标记为已访问
                foreach (Vector2Int neighbor in neighbors)
                {
                    if (!visited.Contains(neighbor))
                    {
                        stack.Push(neighbor);
                        visited.Add(neighbor);
                    }
                }
            }

            return blueCoordinates;
        }
        
        
        private static bool IsBorder(ushort[,] tempAreaData,int areaId,Vector2Int point)
        {
            int[] dx = { 0, 0, -1, 1, -1, -1, 1, 1 };
            int[] dy = { -1, 1, 0, 0, 1, -1, 1, -1 };
            for (int i = 0; i < dx.Length; i++)
            {
                int newX = point.x + dx[i];
                int newY = point.y + dy[i];
                if (newX < 0 || newX >= tempAreaData.GetLength(0))
                    return true;
                if ((newY < 0 || newY >= tempAreaData.GetLength(1)))
                    return true;
                    
                if (tempAreaData[newX,newY] != areaId)
                {
                    return true;
                }
            }
            return false;
        }
        
        private static int GetPriority(ushort[,] tempAreaData,int areaId,Vector2Int point)
        {
            int score = 0;
            int[] dx = { 0, 0, -1, 1, -1, -1, 1, 1 };
            int[] dy = { -1, 1, 0, 0, 1, -1, 1, -1 };
            for (int i = 0; i < dx.Length; i++)
            {
                int newX = point.x + dx[i];
                int newY = point.y + dy[i];
                if (IsBorder(tempAreaData, areaId, new Vector2Int(newX,newY)))
                {
                    score += 1;
                }
            }
            return score;
        }
        
        #endregion
        
#endif
    }
}