//------------------------------------------------------------
// Game Framework
// Copyright © 2013-2021 <PERSON>. All rights reserved.
// Homepage: https://gameframework.cn/
// Feedback: mailto:<EMAIL>
//------------------------------------------------------------

using GameFramework.UI;
using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using UnityGameFramework.Runtime;

namespace Game.Hotfix
{
    public static class UIExtension
    {
        #region UIForm 扩展接口

        // ================================= 是否存在 UIForm 实例 =================================

        public static bool HasUIForm(this UIComponent uiComponent, EnumUIForm uiFormId, string uiGroupName = null)
        {
            return uiComponent.HasUIForm((int)uiFormId, uiGroupName);
        }

        public static bool HasUIForm(this UIComponent uiComponent, int uiFormId, string uiGroupName = null)
        {
            if (GameEntry.LDLTable.HaseTable<Game.Hotfix.Config.uiform_config>())
            {
                var tbUIForm = GameEntry.LDLTable.GetTableById<Game.Hotfix.Config.uiform_config>(uiFormId);
                if (tbUIForm == null)
                {
                    return false;
                }

                string assetName = AssetUtility.GetUIFormAsset(tbUIForm.assetname);
                if (string.IsNullOrEmpty(uiGroupName))
                {
                    return uiComponent.HasUIForm(assetName);
                }

                IUIGroup uiGroup = uiComponent.GetUIGroup(uiGroupName);
                if (uiGroup == null)
                {
                    return false;
                }

                return uiGroup.HasUIForm(assetName);
            }

            return false;
        }

        public static UGuiForm GetUIForm(this UIComponent uiComponent, EnumUIForm uiFormId, string uiGroupName = null)
        {
            return uiComponent.GetUIForm((int)uiFormId, uiGroupName);
        }

        public static UGuiForm GetUIForm(this UIComponent uiComponent, int uiFormId, string uiGroupName = null)
        {
            if (GameEntry.LDLTable.HaseTable<Game.Hotfix.Config.uiform_config>())
            {
                var tbUIForm = GameEntry.LDLTable.GetTableById<Game.Hotfix.Config.uiform_config>(uiFormId);
                if (tbUIForm == null)
                {
                    return null;
                }

                string assetName = AssetUtility.GetUIFormAsset(tbUIForm.assetname);
                UIForm uiForm = null;
                if (string.IsNullOrEmpty(uiGroupName))
                {
                    uiForm = uiComponent.GetUIForm(assetName);
                    if (uiForm == null)
                    {
                        return null;
                    }

                    return (UGuiForm)uiForm.Logic;
                }

                IUIGroup uiGroup = uiComponent.GetUIGroup(uiGroupName);
                if (uiGroup == null)
                {
                    return null;
                }

                uiForm = (UIForm)uiGroup.GetUIForm(assetName);
                if (uiForm == null)
                {
                    return null;
                }

                return (UGuiForm)uiForm.Logic;
            }

            return null;
        }

        // ================================= 打开 UIForm =================================

        public static int? OpenUIForm(this UIComponent uiComponent, EnumUIForm uiFormId, object userData = null)
        {
            return uiComponent.OpenUIForm((int)uiFormId, userData);
        }

        public static int? OpenUIForm(this UIComponent uiComponent, int uiFormId, object userData = null)
        {
            if (GameEntry.LDLTable.HaseTable<Game.Hotfix.Config.uiform_config>())
            {
                var tbUIForm = GameEntry.LDLTable.GetTableById<Game.Hotfix.Config.uiform_config>(uiFormId);
                if (tbUIForm == null)
                {
                    Log.Warning("Can not load UI form '{0}' from data table.", uiFormId.ToString());
                    return null;
                }

                string assetName = AssetUtility.GetUIFormAsset(tbUIForm.assetname);

                // 不允许多个界面实例
                if (!tbUIForm.allowmultiinstance)
                {
                    if (uiComponent.IsLoadingUIForm(assetName))
                    {
                        return null;
                    }

                    // 界面已打开，重新激活界面，把界面提到最上层
                    if (uiComponent.HasUIForm(assetName))
                    {
                        UIForm uiForm = uiComponent.GetUIForm(assetName);
                        if (uiForm == null)
                        {
                            Log.Warning("Can not find UI form '{0}'.", assetName);
                            return null;
                        }
                        uiComponent.RefocusUIForm(uiForm, userData);
                        return null;
                    }
                }

                return uiComponent.OpenUIForm(assetName, tbUIForm.uigroupname, Constant.AssetPriority.UIFormAsset, tbUIForm.pausecovereduiform, userData);
            }

            return null;
        }

        // ================================= 关闭 UIForm =================================

        public static void CloseUIForm(this UIComponent uiComponent, UGuiForm uiForm)
        {
            uiComponent.CloseUIForm(uiForm.UIForm);
        }

        public static void CloseUIForm(this UIComponent uiComponent, EnumUIForm uiFormId, object userData = null)
        {
            UGuiForm uGuiForm = uiComponent.GetUIForm(uiFormId);
            if (uGuiForm == null)
            {
                Debug.LogWarningFormat("Can not find UGuiForm '{0}'.", uiFormId.ToString());
                return;
            }
            uiComponent.CloseUIForm(uGuiForm.UIForm, userData);
        }

        // ================================= 刷新 UIForm =================================

        public static void RefreshUIForm(this UIComponent uiComponent, EnumUIForm uiFormId, object userData = null)
        {
            UGuiForm uGuiForm = uiComponent.GetUIForm(uiFormId);
            if (uGuiForm == null)
            {
                Log.Error("Can not find UGuiForm '{0}'.", uiFormId.ToString());
                return;
            }
            uGuiForm.OnRefresh(userData);
        }

        // ================================= 打开通用确认弹窗 =================================

        public static void OpenCommonConfirm(this UIComponent uiComponent, DialogParams dialogParams)
        {
            uiComponent.OpenUIForm(EnumUIForm.UICommonConfirmForm, dialogParams);
        }

        // ================================= 关闭允许被 Esc 键关闭的顶层界面 =================================

        public static void CloseTopUIFormByEsc(this UIComponent uiComponent)
        {
            List<UIForm> uiForms = new();

            // 获取所有已加载的界面
            uiComponent.GetAllLoadedUIForms(uiForms);

            // 层级排序
            uiForms.Sort((a, b) =>
            {
                // 先排序组
                if (a.UIGroup.Depth != b.UIGroup.Depth)
                {
                    return a.UIGroup.Depth.CompareTo(b.UIGroup.Depth);
                }
                // 再排序组内深度
                else if (a.DepthInUIGroup != b.DepthInUIGroup)
                {
                    return a.DepthInUIGroup.CompareTo(b.DepthInUIGroup);
                }
                return 0;
            });

            // 从上往下遍历，找到第一个允许被 Esc 键关闭的界面并关闭
            for (int i = uiForms.Count - 1; i >= 0 ; i--)
            {
                // 界面资源路径
                string assetName = uiForms[i].UIFormAssetName;

                // 裁剪出界面名称
                int startIndex = assetName.LastIndexOf('/') + 1;
                int endIndex = assetName.LastIndexOf('.');
                string name = assetName[startIndex..endIndex];

                // 字符串转枚举
                EnumUIForm enumUIForm = (EnumUIForm)Enum.Parse(typeof(EnumUIForm), name);
                
                // 检查配置
                if (GameEntry.LDLTable.HaseTable<Config.uiform_config>())
                {
                    var tbUIForm = GameEntry.LDLTable.GetTableById<Config.uiform_config>((int)enumUIForm);
                    
                    // 允许被 Esc 键关闭
                    if (tbUIForm.allow_esc_close)
                    {
                        UGuiForm uGuiForm = uiForms[i].Logic as UGuiForm;
                        if (uGuiForm != null)
                        {
                            // 先调用 HandleEscapeKey，如果返回 true 表示已处理（如隐藏子面板），不关闭界面
                            if (uGuiForm.HandleEscapeKey())
                            {
                                return; // 已处理，不关闭界面
                            }

                            // 如果返回 false，正常关闭界面
                            uGuiForm.Close();
                        }
                        return;
                    }
                }
            }
        }

        #endregion

        #region UI 组件扩展接口

        public static void SetGrey(this Image image, bool isGrey, bool isBlack = false, float blackFactor = 1)
        {
            string targetShaderName = isGrey ? "UIShader/UIMaterialGrey" : "UI/Default";
            
            // 如果材质为空或者 shader 不匹配时才需要重新创建材质
            if (image.material == null || image.material.shader.name != targetShaderName)
            {
                Shader shader = Shader.Find(targetShaderName);
                Material material = new(shader);
                
                if (isGrey && isBlack)
                {
                    material.SetFloat("_IsBlack", blackFactor);
                }
                
                image.material = material;
            }
        }

        public static IEnumerator FadeToAlpha(this CanvasGroup canvasGroup, float alpha, float duration)
        {
            float time = 0f;
            float originalAlpha = canvasGroup.alpha;
            while (time < duration)
            {
                time += Time.deltaTime;
                canvasGroup.alpha = Mathf.Lerp(originalAlpha, alpha, time / duration);
                yield return new WaitForEndOfFrame();
            }

            canvasGroup.alpha = alpha;
        }

        public static IEnumerator SmoothValue(this Slider slider, float value, float duration)
        {
            float time = 0f;
            float originalValue = slider.value;
            while (time < duration)
            {
                time += Time.deltaTime;
                slider.value = Mathf.Lerp(originalValue, value, time / duration);
                yield return new WaitForEndOfFrame();
            }

            slider.value = value;
        }

        #endregion
    }
}
