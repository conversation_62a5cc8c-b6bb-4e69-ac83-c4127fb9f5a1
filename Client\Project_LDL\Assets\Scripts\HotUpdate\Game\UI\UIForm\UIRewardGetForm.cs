using System;
using UnityEngine;
using UnityEngine.UI;
using DG.Tweening;
using System.Collections.Generic;
using Game.Hotfix.Config;

namespace Game.Hotfix
{
    public partial class UIRewardGetForm : UGuiFormEx
    {
        private RectTransform contentRect;
        private float viewportHeight;
        private ScrollRect scrollRect;

        private bool isRewardSpecial = false;
        private Action CloseFunc;
        
        protected override void OnInit(object userData)
        {
            base.OnInit(userData);
            scrollRect = m_goList.GetComponent<ScrollRect>();
            contentRect = scrollRect.content;
            viewportHeight = scrollRect.viewport.rect.height;
            InitBind();
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);
            List<reward> rewards = new List<reward>();

            if (userData is RewardGetParams info)
            {
                rewards.AddRange(info.rewardList);
                m_txtDesc.text = info.descStr;
                m_goDesc.SetActive(info.isNeedShowDesc);
                isRewardSpecial = info.isRewardSpecial;
                CloseFunc = info.closeFunc;
            }
            else if (userData is List<reward> rewardInfo)
            {
                rewards.AddRange(rewardInfo);
                m_goDesc.SetActive(false);
                isRewardSpecial = false;
            }

            var rect = m_goLayout.GetComponent<RectTransform>();
            LayoutRebuilder.ForceRebuildLayoutImmediate(rect);

            scrollRect.normalizedPosition = new Vector2(0, 1);
            m_goEffect.SetActive(false);
            // isSpecial:特殊处理，标题显示“领取成功”  其他默认显示“恭喜获得”
            var nameStr = ToolScriptExtend.GetLang(isRewardSpecial ? 1100300 : 1100063);
            m_txtTittle.text = nameStr;

            var rewardList = new List<reward>();
            //奖励列表排序
            if (rewards != null && rewards.Count > 0)
            {
                foreach (var item in rewards)
                {
                    if (ToolScriptExtend.GetItemConfig(item.item_id) != null)
                    {
                        rewardList.Add(item);
                    }
                }

                // 需要展示获取的钻石，且钻石在道具列表里占首位
                // 1、仅限payment里给的钻石（普通钻石仍按道具规则进行排序）
                // 2、这个特殊处理可以适用于“领取成功”的界面
                // 3、其他道具的排序
                // 1）优先品质
                // 2）其次按道具的优先级进行排序

                rewardList.Sort((x, y) =>
                {
                    var xConfig = ToolScriptExtend.GetItemConfig(x.item_id);
                    var yConfig = ToolScriptExtend.GetItemConfig(y.item_id);

                    if (isRewardSpecial)
                    {
                        var xflag1 = x.item_id == itemid.itemid_6 ? 1 : 0;
                        var yflag1 = y.item_id == itemid.itemid_6 ? 1 : 0;
                        if (xflag1 != yflag1) return -(xflag1 - yflag1);
                    }

                    var xflag2 = (int)xConfig.quality;
                    var yflag2 = (int)yConfig.quality;
                    if (xflag2 != yflag2) return -(xflag2 - yflag2);

                    var xflag3 = xConfig.index;
                    var yflag3 = yConfig.index;
                    if (xflag3 != yflag3) return -(xflag3 - yflag3);
                    return (int)x.item_id - (int)y.item_id;
                });
            }
            ShowRewardList(rewardList);
            m_goEffect.SetActive(true);
            
            GameEntry.Sound.PlaySound(200002);
        }

        // protected override void OnDepthChanged(int uiGroupDepth, int depthInUIGroup)
        // {
        //     base.OnDepthChanged(uiGroupDepth, depthInUIGroup);
        //     SetParticleSystemSortingOrder(m_goEffect, Depth);
        //
        //     var canvas = m_goTitle.GetComponent<Canvas>();
        //     if (canvas != null)
        //     {
        //         canvas.sortingOrder = Depth + 100;
        //     }
        // }

        
        protected override void OnClose(bool isShutdown, object userData)
        {
            base.OnClose(isShutdown, userData);
            
            CloseFunc?.Invoke();
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
        }
        
        private void OnBtnCloseClick()
        {
            Close();
        }
        
        private void OnBtnMaskClick()
        {
            Close();
        }
        
        private void ShowRewardList(List<reward> rewardList)
        {
            if (rewardList.Count == 0) return;
            ToolScriptExtend.ClearAllChild(m_goContent.transform);
            m_goRewardObj.SetActive(false);
            var dataCount = rewardList.Count;
            for (var i = 0; i < dataCount; i++)
            {
                reward reward = rewardList[i];

                item_config itemConfig = ToolScriptExtend.GetItemConfig(reward.item_id);
                var obj = Instantiate(m_goRewardObj,m_goContent.transform);
                obj.SetActive(true);
                
                var node = obj.transform.Find("node");
                var txtName = obj.transform.Find("txt").GetComponent<Text>();
                if (itemConfig != null)
                {
                    txtName.text = ToolScriptExtend.GetLang(itemConfig.name);
                }
                else{
                    Debug.LogError("itemConfig is null" + reward.item_id);
                    txtName.text = "";
                }
                node.localScale = Vector3.one * 0.76f;
                if (node.childCount == 0)
                {
                    BagManager.CreatItem(node,reward.item_id,reward.num, (module) =>
                    {
                        var child = node.GetChild(0);
                        ToolScriptExtend.SetItemObjTxtScale(child.gameObject,1.3f);
                        module.SetClick(module.OpenTips);
                    });
                }
                else
                {
                    var child = node.GetChild(0);
                    var module = child.GetComponent<UIItemModule>();
                    if (module != null)
                    {
                        var itemModule = new ItemModule();
                        itemModule.SetData(reward.item_id,reward.num);
                        module.itemModule = itemModule;
                        module.Init(node,reward.item_id,reward.num);
                        module.DisplayInfo();
                        module.SetClick(module.OpenTips);
                        ToolScriptExtend.SetItemObjTxtScale(module.gameObject,1.3f);
                    }
                }
            }
            var sequence = DOTween.Sequence();
            sequence.AppendInterval(0.5f);
            sequence.AppendCallback(() =>
            {
                LayoutRebuilder.ForceRebuildLayoutImmediate(contentRect);
                var contentHeight = contentRect.rect.height;
                if (contentHeight > viewportHeight)
                {
                    var height = contentRect.rect.height-viewportHeight;
                    var offset1 = (int)height / 270;
                    var offset2 = height % 270 !=0 ?1:0;
                    var time = (offset1 + offset2) * 0.15f;
                    contentRect.DOAnchorPos(new Vector2(0, height), time);
                }
            });
        }

        private void Update()
        {
            if (Input.GetKeyDown(KeyCode.Space))
            {
                GameEntry.UI.OpenUIForm(EnumUIForm.UILimitGiftForm);

            }
        }
    }
}
