using UnityEngine;
using UnityGameFramework.Runtime;

namespace Game.Hotfix
{
    [DisallowMultipleComponent]
    [AddComponentMenu("GameCustom/KeyboardComponent")]
    public class KeyboardComponent : GameFrameworkComponent
    {
        void Start()
        {
            
        }

        void Update()
        {
            if (Input.GetKeyDown(KeyCode.Escape))
            {
                GameEntry.UI.CloseTopUIFormByEsc();
            }
            else if (Input.GetKeyDown(KeyCode.Q))
            {
                GameEntry.PushView.AddPushView(EnumUIForm.UIBagForm, null);
            }
        }
    }
}
