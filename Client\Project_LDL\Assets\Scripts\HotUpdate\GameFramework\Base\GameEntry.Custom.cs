﻿using GameFramework;
using GameFramework.Resource;
using GameFramework.UI;
using System;
using System.Collections.Generic;
using System.Reflection;
using UnityEngine;
using UnityGameFramework.Runtime;
/// <summary>
/// 游戏入口。
/// </summary>
namespace Game.Hotfix
{
    public partial class GameEntry
    {
        public static LDLTableComponent LDLTable => _LDLTable ??= UnityGameFramework.Runtime.GameEntry.GetComponent<LDLTableComponent>();
        private static LDLTableComponent _LDLTable;
        
        public static CameraComponent Camera => _camera ??= UnityGameFramework.Runtime.GameEntry.GetComponent<CameraComponent>();
        private static CameraComponent _camera;

        public static LDLNetComponent LDLNet => _ldlNet ??= UnityGameFramework.Runtime.GameEntry.GetComponent<LDLNetComponent>();
        private static LDLNetComponent _ldlNet;

        public static HUDComponent HUD => _hud ??= UnityGameFramework.Runtime.GameEntry.GetComponent<HUDComponent>();
        private static HUDComponent _hud;
        
        public static HTTPComponent Http => _http ??= UnityGameFramework.Runtime.GameEntry.GetComponent<HTTPComponent>();
        private static HTTPComponent _http;
        
        public static LogicDataComponent LogicData => _logicData ??= UnityGameFramework.Runtime.GameEntry.GetComponent<LogicDataComponent>();
        private static LogicDataComponent _logicData;

        public static TimeComponent Time => _Time ??=UnityGameFramework.Runtime.GameEntry.GetComponent<TimeComponent>();
        private static TimeComponent _Time;
        
        public static CommonEffectComponent Effect => _effect ??=UnityGameFramework.Runtime.GameEntry.GetComponent<CommonEffectComponent>();
        private static CommonEffectComponent _effect;

        public static GotoComponent Goto => _Goto = _Goto != null ? _Goto : UnityGameFramework.Runtime.GameEntry.GetComponent<GotoComponent>();
        private static GotoComponent _Goto;

        public static PushViewComponent PushView => _PushView = _PushView != null ? _PushView : UnityGameFramework.Runtime.GameEntry.GetComponent<PushViewComponent>();
        private static PushViewComponent _PushView;
        
        public static EquipmentData EquipmentData => LogicData.EquipmentData;
        public static PaymentData PaymentData => LogicData.PaymentData;
        public static RoleData RoleData => LogicData.RoleData;
        public static TradeTruckData TradeTruckData => LogicData.TradeTruckData;
        
        private static Action m_action;
        /// <summary>
        /// 加载自定义组件
        /// </summary>
        private static void LoadCustomComponent()
        {
            Game.GameEntry.Resource.LoadAsset("Assets/ResPackage/Prefab/GF/Customs.prefab", new LoadAssetCallbacks(LoadAssetSuccessCallback, LoadAssetFailureCallback));
        }

        private static void LoadAssetFailureCallback(string assetName, LoadResourceStatus status, string errorMessage, object userData)
        {
            throw new GameFrameworkException(errorMessage);
        }

        private static void LoadAssetSuccessCallback(string assetName, object asset, float duration, object userData)
        {
            if (GameObject.Find("Entry/GameFramework/HOT") != null)
            {
                Resource.UnloadAsset(asset);
                return;
            }
            GameObject gameObject = UnityEngine.Object.Instantiate((GameObject)asset, GameObject.Find("Entry/GameFramework").transform, true);
            gameObject.name = "HOT";
            gameObject.transform.position = Vector3.zero;

            m_action?.Invoke();
            //ResetUIFormHelper();
        }
        public static void Entrance(Action action)
        {
            m_action = action;
            //初始化自定义调试器
            LoadCustomComponent();
        }
    }
}
