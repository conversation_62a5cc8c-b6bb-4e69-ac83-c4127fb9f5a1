using System;
using Activity;

namespace Game.Hotfix
{
    public class Chao<PERSON><PERSON>_RoleRecharge:ChaoZhiRecharge
    {
        //实例化对象时初始化
        public override void OnInit()
        {
            base.OnInit();
            m_goPrefab.SetActive(false);
            isTimer = false;
            startRatio = 0.06f;
            var bindData = GetBindData();
            if (bindData != null)
            {
                if (bindData is ActivityTime data)
                {
                    activityMsg = data;
                    InitFormData();
                    ChaoZhiManager.C2SLoadActivityInfo(activityMsg);
                }
            }
        }

        //再次打开
        public override void OnReOpen()
        {
            ScrollToTargetIndex();
        }

        //本界面刷新，一般由UISwitchPage.RefreshCurPage触发
        public override void OnRefreshSelf()
        {
            base.OnRefreshSelf();
        }


        //事件刷新
        public override void OnRefresh(object userData)
        {
            var data = (ValueTuple<string, int>)userData;
            var flag = data.Item1;
            var param = data.Item2;
            if (flag == "Chao<PERSON>hi_RoleRecharge")
            {
                if (param == 1)
                {
                    InitFormData();
                }
            }
        }

        /// <summary>
        /// 被选中触发的逻辑
        /// </summary>
        /// <param name="isOn">是否被选中</param>
        public override void OnSelect(bool isOn)
        {
        }

        //隐藏面板时逻辑
        public override void OnClose()
        {
        }

        //每帧更新逻辑，类似于Unity的Update函数
        public override void OnUpdate()
        {
        }

        //计时器逻辑
        public override void OnTimer()
        {
            if (!isTimer) return;
            var temp = timerCount - 1;
            if (temp >= 0)
            {
                timerCount--;
                if (m_txtTimer != null)
                {
                    m_txtTimer.text = ToolScriptExtend.FormatTime(timerCount);
                }
                if (timerCount == 0)
                {
                    isTimer = false;
                    //初始化请求
                  
                }
            }
        }

        //资源释放
        public override void Release()
        {
            
        }
    }
}

