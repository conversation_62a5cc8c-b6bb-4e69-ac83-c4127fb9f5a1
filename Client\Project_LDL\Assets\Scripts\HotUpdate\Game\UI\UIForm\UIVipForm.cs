using System;
using System.Collections.Generic;
using System.Linq;
using Game.Hotfix.Config;
using GameFramework.Event;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.UI;

namespace Game.Hotfix
{
    public partial class UIVipForm : UGuiFormEx
    {
        private int sumCount;
        private int curIndex;
        private int curDisplayLevel;//当前展示页的vip等级
        private UnityAction<int> callback;//外部逻辑回调

        private RectTransform content;

        private VipData Manager;
        
        private UIText m_txtUnlock;

        private bool isUnlockTimer = false;
        private int unlockCount = 0;
        
        private UIText m_txtFree;
        private bool isFreeTimer = false;
        private int freeCount = 0;

        private bool isDelayPop;//是否需要延迟弹出等级提升界面
        public bool lastBoxStatus = false;

        protected override void OnInit(object userData)
        {
            base.OnInit(userData);
            Manager = GameEntry.LogicData.VipData;
            content = m_scrollviewList.content;
            m_txtUnlock = m_btnUnlock.transform.Find("Text").GetComponent<UIText>();
            m_txtFree = m_goFreeGift.transform.Find("btnLock/txt").GetComponent<UIText>();
            InitBind();
            m_goPrefab.SetActive(false);
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);
            isDelayPop = false;
            Manager.DebugVipMsg();
            
            var isReceived = Manager.IsVipDailyPointReceive();
            lastBoxStatus = !isReceived;
            m_goColserEffect.SetActive(false);
            m_goGetEffect.SetActive(false);
            
            curIndex = 0;
            if (userData != null)
            {
                int receiveIndex = Convert.ToInt32(userData);
                if (receiveIndex == 0)
                {
                    // 获取当前 VIP 等级
                }
                else
                {
                    curIndex = receiveIndex - 1;
                }
            }
            
            isUnlockTimer = false;
            unlockCount = 0;
            isFreeTimer = false;
            freeCount = 0;
            var maxValue = Manager.GetMaxLevel();
            InitGroup(maxValue, (index) =>
            {
                curDisplayLevel = curIndex + 1;
                SwitchPageView();
                FrequentChange();
            });
            UpdatePageView();
            CheckBtnActive();
            
            //开启一个定时器
            Timers.Instance.Add(GetInstanceID().ToString(), 1, (a) =>
            {
                OnTimerFunc();
                
            }, 86400);
            
            
            GameEntry.Event.Subscribe(VipChangeEventArgs.EventId, OnVipChangeFunc);
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            base.OnClose(isShutdown, userData);
            Timers.Instance.Remove(GetInstanceID().ToString());
            GameEntry.Event.Unsubscribe(VipChangeEventArgs.EventId, OnVipChangeFunc);
            isDelayPop = false;
        }
        
        //vip信息更新
        private void OnVipChangeFunc(object sender, GameEventArgs e)
        {
            UpdatePageView();
            FrequentChange();
        }
        
        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
            var flag = (int)userData;
            if (flag == 1)
            {
            }
            else if(flag == 2)
            {
                //vip升级
                var form = GameEntry.UI.GetUIForm(EnumUIForm.UIMallForm);
                if (form != null)
                {
                    isDelayPop = true;
                }
                else
                {
                    ShowUpgradeDialog();
                }
            }
            else if (flag == 3)
            {
                if (isDelayPop)
                {
                    ShowUpgradeDialog();
                }
            }
        }

        private void ShowUpgradeDialog()
        {
            var level = Manager.GetVipLevel();
            curIndex = level - 1;
            CheckBtnActive();
            GameEntry.UI.OpenUIForm(EnumUIForm.UIVipUpgradeForm);
            GameEntry.UI.CloseUIForm(EnumUIForm.UIVipPointBuyForm);
            GameEntry.UI.CloseUIForm(EnumUIForm.UIVipPointTipForm);
        }
        
        private void OnBtnExitClick()
        {
            Close();
            
        }
        
        private void OnBtnStoreClick()
        {
            GameEntry.UI.OpenUIForm(EnumUIForm.UIGeneralShopForm,storetype.storetype_Vip);
        }
        
        private void OnBtnVipPointClick()
        {
            GameEntry.UI.OpenUIForm(EnumUIForm.UIVipPointBuyForm);
        }
        
        private void OnBtnUnlockClick()
        {
            GameEntry.UI.OpenUIForm(EnumUIForm.UIVipTimeBuyForm);
            
        }
        
        private void OnBtnLeftClick()
        {
            var nextIndex = curIndex - 1;
            if (nextIndex < 0)
            {
                return;
            }
            curIndex = nextIndex;
            CheckBtnActive();
            
        }
        
        private void OnBtnRightClick()
        {
            var nextIndex = curIndex + 1;
            if (nextIndex >= sumCount)
            {
                return;
            }
            curIndex = nextIndex;
            CheckBtnActive();
        }
        
        //领取vip免费点数
        private void OnBtnFreeClick()
        {
            var isReceived = Manager.IsVipDailyPointReceive();
            if (!isReceived)
            {
                Manager.C2SVipReceiveDailyPoints();
            }
        }

        private void OnTimerFunc()
        {
            if (isUnlockTimer)
            {
                var temp = unlockCount - 1;
                if (temp >= 0)
                {
                    unlockCount--;
                    m_txtUnlock.text = ToolScriptExtend.FormatTime(unlockCount);
                    if (unlockCount == 0)
                    {
                        isUnlockTimer = false;
                        m_txtUnlock.text = ToolScriptExtend.GetLang(1100150);
                    }
                }
            }
            
            if (isFreeTimer)
            {
                var temp = freeCount - 1;
                if (temp >= 0)
                {
                    freeCount--;
                    m_txtFree.text = ToolScriptExtend.FormatTime(freeCount);
                    if (freeCount == 0)
                    {
                        isFreeTimer = false;
                        m_txtFree.text = ToolScriptExtend.GetLang(1100345);//未解锁
                    }
                }
            }
        }
        
        private void InitGroup(int _sumCount,UnityAction<int> _callback)
        {
            sumCount = _sumCount;
            callback = _callback;
        }
    
        //按钮显隐逻辑
        private void CheckBtnActive()
        {
            if (sumCount <= 1)
            {
                m_btnLeft.gameObject.SetActive(false);
                m_btnRight.gameObject.SetActive(false);
            }
            else
            {
                m_btnLeft.gameObject.SetActive(curIndex >= 1);
                m_btnRight.gameObject.SetActive(curIndex <= sumCount-2);
            }
 
            var showIndex = curIndex + 1;
            callback?.Invoke(showIndex);
        }

        //后端信息改变时更新
        private void UpdatePageView()
        {
            var curConfig = Manager.GetVipConfigById(curDisplayLevel);
            if (curConfig == null) return;
            
            var day = GameEntry.LogicData.RoleData.LoginContinueDays;
            var points = GameEntry.LogicData.VipData. GetContinuePoints(day+1);
            m_txtTitleDesc.text = ToolScriptExtend.GetLangFormat(1100335, day.ToString(), $"<color=#98FF57>{points}</color>");
            
            var level = Manager.GetVipLevel();
            //vip等级
            m_txtVip1.text = level.ToString();
            m_txtVip2.text = $"VIP{level}";

            //vip免费点数是否可领取
            SetFreePointsStatus();
            
            //经验进度
            SetProgressInfo();
            
            CheckUnlockArea();
        }

        //点击切换按钮时更新界面显示
        private void SwitchPageView()
        {
            var curConfig = Manager.GetVipConfigById(curDisplayLevel);
            if (curConfig == null) return;
            
            //特权列表
            ShowPrivilegeList(curConfig);
            
        }
        
        //后端信息改变时更新 && 点击切换按钮时更新界面显示
        private void FrequentChange()
        {
            var curConfig = Manager.GetVipConfigById(curDisplayLevel);
            if (curConfig == null) return;

            var isActive = Manager.IsVipActive();
            var typeStr = ToolScriptExtend.GetLangFormat(1100336, curDisplayLevel.ToString());
            if (!isActive)
            {
                typeStr += $" ({ToolScriptExtend.GetLang(1100345)})";
            }
            m_txtType.text = typeStr;
            
            //每日免费礼包
            SetFreeGiftInfo(curConfig.vip_reward_free);
                
            //vip专属礼包
            var giftId = curConfig.vip_gift_pack;
            var curLevel = Manager.GetVipLevel();
            if (curDisplayLevel > curLevel)
            {
                //未解锁
                SetExclusiveGiftInfo(giftId,false);
            }
            else
            {
                //可购买||已购买
                GameEntry.LogicData.MallData.C2SGiftGetWay(giftId, (resultId) =>
                {
                    SetExclusiveGiftInfo(giftId,resultId == 0);
                });
            }
        }
        
        public class DataNode
        {
            public bool isNew;
            public string desc;
            public string value;
            public int priority;
            public int type;
        }
        
        //展示特权
        private void ShowPrivilegeList(vip_level curData)
        {
            var thisLevel = curData.id;
            if (!ToolScriptExtend.GetConfigById<vip_level>(thisLevel - 1, out var lastData)) return;
            
            var vipPrivilegeShow = GameEntry.LDLTable.GetTable<vip_privilege_show>();
            if(vipPrivilegeShow == null)return;
            
            var showList = new List<DataNode>();
            // var root = content.transform;
            var list = curData.vip_attributes;
            var lastDataList = lastData.vip_attributes;
            // var mallManager = GameEntry.LogicData.MallData;
            // mallManager.RecycleOrCreate( m_goPriviligeItem,root,list.Count);
            for (var i = 0; i < list.Count; i++)
            {
                var isNew = true;
                var type = list[i].attributes_type;
                var lastInfo = lastDataList.FirstOrDefault(x => x.attributes_type == type);
                if (lastInfo != null)
                {
                    if (lastInfo.value == list[i].value)
                    {
                        isNew = false;
                        // break;
                    }
                }
                
                var resultStr = "";
                var calculateType = list[i].value_type;
                var calculateValue = list[i].value;
                if (calculateType == valuetype.valuetype_1)
                {
                    resultStr = calculateValue.ToString();
                }
                else if (calculateType == valuetype.valuetype_2)
                {
                    resultStr = ((calculateValue / 10000.0f)*100).ToString("0.00") + "%";
                }
                else if (calculateType == valuetype.valuetype_3)
                {
                    resultStr = calculateValue.ToString();
                }
                
                showList.Add(new DataNode()
                {
                    isNew = isNew,
                    desc = ToolScriptExtend.GetNameByAttrbuteType(type),
                    value = resultStr,
                    priority = GetPriority(privilegetype.privilegetype_attribute,type),
                    type = (int)privilegetype.privilegetype_attribute
                });
            }
            
            if (curData.vip_show)
            {
                showList.Add(new DataNode()
                {
                    isNew = !lastData.vip_show,
                    desc = ToolScriptExtend.GetLang(1100306),
                    value = "1",
                    priority = GetPriority(privilegetype.privilegetype_privilege,"vip_show"),
                    type = (int)privilegetype.privilegetype_privilege
                });
            }
            
            if (curData.collection_resources)
            {
                showList.Add(new DataNode()
                {
                    isNew = !lastData.collection_resources,
                    desc = ToolScriptExtend.GetLang(1100307),
                    value = "1",
                    priority = GetPriority(privilegetype.privilegetype_privilege,"collection_resources"),
                    type = (int)privilegetype.privilegetype_privilege
                });
            }
            
            if (curData.vip_chat_show)
            {
                showList.Add(new DataNode()
                {
                    isNew = !lastData.vip_chat_show,
                    desc = ToolScriptExtend.GetLang(1100308),
                    value = "1",
                    priority = GetPriority(privilegetype.privilegetype_privilege,"vip_chat_show"),
                    type = (int)privilegetype.privilegetype_privilege
                });
            }

            var sprints = curData.acceleration_sprints;
            if (sprints > 0)
            {
                showList.Add(new DataNode()
                {
                    isNew = sprints > lastData.acceleration_sprints,
                    desc = ToolScriptExtend.GetLang(1100303),
                    value = sprints.ToString(),
                    priority = GetPriority(privilegetype.privilegetype_privilege,"acceleration_sprints"),
                    type = (int)privilegetype.privilegetype_privilege
                });
            }
            
            if (curData.automatic_dispatch)
            {
                showList.Add(new DataNode()
                {
                    isNew = !lastData.automatic_dispatch,
                    desc = ToolScriptExtend.GetLang(1100305),
                    value = "1",
                    priority = GetPriority(privilegetype.privilegetype_privilege,"automatic_dispatch"),
                    type = (int)privilegetype.privilegetype_privilege
                });
            }

            if (curData.vip_shake)
            {
                showList.Add(new DataNode()
                {
                    isNew = !lastData.vip_shake,
                    desc = ToolScriptExtend.GetLang(1100304),
                    value = "1",
                    priority = GetPriority(privilegetype.privilegetype_privilege,"vip_shake"),
                    type = (int)privilegetype.privilegetype_privilege
                });
            }
            
            if (curData.quick_challenge)
            {
                showList.Add(new DataNode()
                {
                    isNew = !lastData.quick_challenge,
                    desc = ToolScriptExtend.GetLang(1100310),
                    value = "1",
                    priority = GetPriority(privilegetype.privilegetype_privilege,"quick_challenge"),
                    type = (int)privilegetype.privilegetype_privilege
                });
            }
            
            if (curData.vip_super)
            {
                showList.Add(new DataNode()
                {
                    isNew = !lastData.vip_super,
                    desc = ToolScriptExtend.GetLang(1100309),
                    value = "1",
                    priority = GetPriority(privilegetype.privilegetype_privilege,"vip_super"),
                    type = (int)privilegetype.privilegetype_privilege
                });
            }
          
            if (curData.vip_castle)
            {
                showList.Add(new DataNode()
                {
                    isNew = !lastData.vip_castle,
                    desc = ToolScriptExtend.GetLang(1100311),
                    value = "1",
                    priority = GetPriority(privilegetype.privilegetype_privilege,"vip_castle"),
                    type = (int)privilegetype.privilegetype_privilege
                });
            }
            
            showList.Sort((x, y) =>
            {
                var flag1X = x.isNew ? 1 : 0;
                var flag1Y = y.isNew ? 1 : 0;
                if (flag1X != flag1Y) return flag1Y - flag1X;
                if (x.type != y.type) return y.type - x.type;
                return x.priority - y.priority;
            });

            var newList = new List<DataNode>();
            var oldList = new List<DataNode>();

            foreach (var data in showList)
            {
                if (data.isNew)
                {
                    newList.Add(data);
                }
                else
                {
                    oldList.Add(data);
                }
            }
            
            //New标签  特权类型 2>1
            newList.Sort((x, y) =>
            {
                if (x.type != y.type) return y.type - x.type;
                return x.priority - y.priority;
            });
            
            //非New标签  特权类型 1>2
            oldList.Sort((x, y) =>
            {
                if (x.type != y.type) return x.type - y.type;
                return x.priority - y.priority;
            });

            var resultList = newList.Concat(oldList).ToList();
            var root = content.transform;
            var mallManager = GameEntry.LogicData.MallData;
            mallManager.RecycleOrCreate( m_goPriviligeItem,root,resultList.Count);
            for (var i = 0; i < resultList.Count; i++)
            {
                var child = root.GetChild(i);
                InitSinglePrivilegeInfo(resultList[i],child.gameObject);
            }
            
            m_scrollviewList.normalizedPosition = new Vector2(0, 1);
            LayoutRebuilder.ForceRebuildLayoutImmediate(content);
        }

        public int GetPriority(privilegetype type,object flag)
        {
            var configs = GameEntry.LDLTable.GetTable<vip_privilege_show>();
            if (configs == null) return 0;
            if (type == privilegetype.privilegetype_attribute)
            {
                var id = (attributes_type)flag;
                var data = configs.FirstOrDefault(x => x.attributes_type == id);
                if (data != null)
                {
                    return data.priority;
                }
            }
            else if (type == privilegetype.privilegetype_privilege)
            {
                var name = flag.ToString();
                var data = configs.FirstOrDefault(x => x. privilege_field_name == name);
                if (data != null)
                {
                    return data.priority;
                }
            }
            return 0;
        }
        private void InitSinglePrivilegeInfo(DataNode data,GameObject obj)
        {
            var root = obj.transform;
            var mainRect = root.GetComponent<RectTransform>();
            var newMark = root.Find("newMark");//"新"标记
            var desc = root.Find("desc").GetComponent<UIText>();//特权描述
            var descRect = desc.GetComponent<RectTransform>();
            var value = root.Find("value").GetComponent<UIText>();
            
            var isNew = data.isNew;
            newMark.gameObject.SetActive(isNew);
            
            var pos = isNew ? new Vector2(150, 0) : new Vector2(57, 0);
            descRect.anchoredPosition = pos;
            desc.text = data.desc;
            value.text = data.value;
            
            LayoutRebuilder.ForceRebuildLayoutImmediate(descRect);
            var height = descRect.rect.height;
            mainRect.SetSizeWithCurrentAnchors(RectTransform.Axis.Vertical,height);
        }
        
        //设置每日免费奖励领取状态
        private void SetFreePointsStatus()
        {
            m_goColserEffect.SetActive(false);   
            var isReceived = Manager.IsVipDailyPointReceive();
            var isOk = !isReceived;
            m_goFreeOff.SetActive(!isOk);
            m_goFreeOn.SetActive(isOk);
            m_goGetEffect.SetActive(isOk);
            
            if (lastBoxStatus && isReceived)
            {
                m_goColserEffect.SetActive(true);
                lastBoxStatus = false;
            }
        }
        
        //vip免费礼包
        private void SetFreeGiftInfo(List<reward> rewardList)
        {
            var root = m_goFreeGift.transform;
            var title = root.Find("title").GetComponent<UIText>();
            var box1 = root.Find("box1");
            var box2 = root.Find("box2");
            var rewardRoot = root.Find("Scroll View/Viewport/root");
            var scroll = root.Find("Scroll View").GetComponent<ScrollRect>();
            
            var btnGet = root.Find("btnGet").GetComponent<UIButton>();
            var btnGetTxt = root.Find("btnGet/txt").GetComponent<UIText>();
            var btnLock = root.Find("btnLock").GetComponent<UIButton>();
            
            //礼包标题
            title.text = ToolScriptExtend.GetLangFormat(1100367, curDisplayLevel.ToString());
            
            //礼包状态
            var status = Manager.GetFreeGiftStatus(curDisplayLevel);

            isFreeTimer = false;
            //1：可领取
            //置灰状态：2：已领取 3：未激活 4：未达标
            btnGet.gameObject.SetActive(status == 1);
            btnLock.gameObject.SetActive(status > 1);
            if (status == 1)
            {
                btnGetTxt.text = ToolScriptExtend.GetLang(1100273);//领取
            }
            else
            {
                if (status == 2)
                {
                    freeCount = GameEntry.LogicData.VipData.GetFreeResetTime();
                    if (freeCount > 0)
                    {
                        isFreeTimer = true;
                        m_txtFree.text = ToolScriptExtend.FormatTime(freeCount);
                    }
                }
                else if (status == 3)
                {
                    m_txtFree.text = ToolScriptExtend.GetLang(1100354);//启动vip
                }
                else if (status == 4)
                {
                    m_txtFree.text = ToolScriptExtend.GetLang(1100345);//未解锁
                }
            }
            
            //礼包状态图标
            box1.gameObject.SetActive(status != 2);
            box2.gameObject.SetActive(status == 2);
            
            //奖励列表
            var mallManager = GameEntry.LogicData.MallData;
            mallManager.RecycleOrCreate( m_goRewardItem,rewardRoot,rewardList.Count);
            ToolScriptExtend.ShowRewardList(rewardRoot, rewardList,null,0.45f,1.5f);
            
            scroll.normalizedPosition = new Vector2(0, 1);
            
            ToolScriptExtend.BindBtnLogic(btnGet, () =>
            {
                Manager.C2SVipReceiveDailyGift();
            });
        }
        
        //vip等级专属礼包
        private void SetExclusiveGiftInfo(int giftId,bool isSoldOut)
        {
            if (!ToolScriptExtend.GetConfigById<gift_pack>(giftId, out var data)) return;
            var mallManager = GameEntry.LogicData.MallData;
            
            var root = m_goExclusiveGift.transform;
            var title = root.Find("title").GetComponent<UIText>();
            var rewardRoot = root.Find("Scroll View/Viewport/root");
            var scroll = root.Find("Scroll View").GetComponent<ScrollRect>();
            
            var btnBuy = root.Find("btnBuy").GetComponent<UIButton>();
            var btnBuyTxt = root.Find("btnBuy/txt").GetComponent<UIText>();
            var btnLock = root.Find("btnLock").GetComponent<UIButton>();
            var btnLockTxt = root.Find("btnLock/txt").GetComponent<UIText>();
            
            var discount = root.Find("Image/discount").GetComponent<UIText>();//折扣
            var limit = root.Find("limit").GetComponent<UIText>();
            
            discount.text = $"{data.cost_effectiveness}%";
            limit.text = $"{ToolScriptExtend.GetLang(1100346)}{data.buy_limit_times}";
            var icon = root.Find("icon").GetComponent<UIImage>();
            icon.SetImage(data.gift_pack_icon);
            
            //礼包标题
            title.text = ToolScriptExtend.GetLang(data.gift_pack_name);
            // 1：未解锁 2：可购买 3：已购买
            var status = Manager.GetExclusiveGiftStatus(curDisplayLevel,isSoldOut);
            btnBuy.gameObject.SetActive(status == 2);
            btnLock.gameObject.SetActive(status != 2);
            if (status == 2)
            {
                mallManager.CreateRechargeScore(data.payment_id,btnBuy.transform);
                btnBuyTxt.text = mallManager.GetPrice(data.payment_id);
            }
            else if (status == 1)
            {
                btnLockTxt.text = ToolScriptExtend.GetLang(1100345);//未解锁
            }
            else
            {
                btnLockTxt.text = ToolScriptExtend.GetLang(1055);//"已购买"
            }
            
            //奖励列表
            var rewardList = mallManager.GetRewardList(giftId);
            mallManager.RecycleOrCreate( m_goRewardItem,rewardRoot,rewardList.Count);
            ToolScriptExtend.ShowRewardList(rewardRoot, rewardList,null,0.5f,1.35f);
            scroll.normalizedPosition = new Vector2(0, 1);
            
            ToolScriptExtend.BindBtnLogic(btnBuy, () =>
            {
                GameEntry.PaymentData.Pay(data.payment_id);
            });
        }
        
        //显示经验进度
        private void SetProgressInfo()
        {
            var configs = GameEntry.LDLTable.GetTable<vip_level>();
            var temp = configs.Select(x => x.vip_exp);
            var maxExp = temp.Max();
            var exp = GameEntry.LogicData.VipData.GetVipExp();
            if (exp < 0)
            {
                exp = 0;
            }
            if (exp >= maxExp)
            {
                m_txtProgress.text = ToolScriptExtend.GetLang(711358);//已满级
                m_sliderProgress.value = 1;
            }
            else
            {
                var value = configs.FirstOrDefault(x=>exp < x.vip_exp);
                if (value != null)
                {
                    m_txtProgress.text = $"{exp}/{value.vip_exp}";   
                    m_sliderProgress.value = exp*1.0f/value.vip_exp;
                }
            }
        }
        
        //检查解锁按钮状态
        private void CheckUnlockArea()
        {
            var isActive = Manager.IsVipActive();
            unlockCount = Manager.GetVipRemainTime();
            isUnlockTimer = isActive;
            m_txtUnlock.text = isActive ? ToolScriptExtend.FormatTime(unlockCount): ToolScriptExtend.GetLang(1100150);
            m_goUnlockDot.SetActive(!isActive);
        }
        
    }
}
