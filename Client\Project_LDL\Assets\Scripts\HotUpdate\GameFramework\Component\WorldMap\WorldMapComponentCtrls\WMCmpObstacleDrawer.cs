using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace Game.Hotfix
{
    public class WMCmpObstacleDrawer : IWMCmp
    {
        private Dictionary<Vector2Int, List<int>> m_ShowListDic = new Dictionary<Vector2Int, List<int>>();
        private HashSet<Vector2Int> m_ShowList = new HashSet<Vector2Int>();

        private WorldMapComponent m_WorldMapComponent;

        public WMCmpObstacleDrawer(WorldMapComponent component)
        {
            m_WorldMapComponent = component;
        }

        public void Init()
        {
            m_WorldMapComponent.OnCameraMoveCall += OnCameraMove;
        }

        public void UnInit()
        {
            m_WorldMapComponent.OnCameraMoveCall -= OnCameraMove;
        }

        private void OnCameraMove()
        {
            m_WorldMapComponent.GetInShowLIstByGrid(GameDefine.WorldMapDataGridSize, 1, WorldMapLOD.Level1,
                WorldMapLOD.Level3, out List<Vector2Int> showListNew);
            
            List<Vector2Int> add = showListNew.Except(m_ShowList).ToList();
            List<Vector2Int> remove = m_ShowList.Except(showListNew).ToList();

            foreach (var t in remove)
            {
                Remove(t);
            }

            foreach (var t in add)
            {
                Add(t);
            }
        }

        private void Add(Vector2Int grid)
        {
            if (m_ShowList.Add(grid))
            {
                if (!m_ShowListDic.ContainsKey(grid))
                {
                    var list = GameEntry.LogicData.WorldMapData.GetObstacleList(grid);
                    if (list != null)
                    {
                        var idList = new List<int>();
                        for (int i = 0; i < list.Count; i++)
                        {
                            Vector3Int data = list[i];
                            var path = GetPathById(data.z);
                            int id = GameEntry.Entity.ShowWorldMapDisplay(path, new Vector3(data.x, 0, data.y));
                            idList.Add(id);
                        }

                        m_ShowListDic.Add(grid, idList);
                    }
                }
            }
        }

        private void Remove(Vector2Int grid)
        {
            if (m_ShowList.Remove(grid))
            {
                if (m_ShowListDic.TryGetValue(grid, out var idList))
                {
                    for (int i = 0; i < idList.Count; i++)
                    {
                        var id = idList[i];
                        GameEntry.Entity.HideEntity(id);
                    }

                    m_ShowListDic.Remove(grid);
                }
            }
        }

        private string GetPathById(int id)
        {
            string path = string.Empty;
            Config.map_obstacle_mountain config = GameEntry.LDLTable.GetTableById<Config.map_obstacle_mountain>(id);
            if (config != null && config.pre_id > 0)
            {
                var preCfg = GameEntry.LDLTable.GetTableById<Config.map_buildpre>(config.pre_id);
                path = preCfg.pre;
            }
            return path;
        }
    }
}