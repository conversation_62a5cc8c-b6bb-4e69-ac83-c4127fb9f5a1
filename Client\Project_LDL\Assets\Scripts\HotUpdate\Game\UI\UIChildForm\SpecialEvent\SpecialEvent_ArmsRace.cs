using System;
using System.Collections.Generic;
using System.Linq;
using DG.Tweening;
using Game.Hotfix.Config;
using Race;
using Roledata;
using UnityEngine;
using UnityEngine.UI;

namespace Game.Hotfix
{
    public class SpecialEvent_ArmsRace : SwitchPanelLogic
    {
        [SerializeField] private ScrollRect m_scrollviewList;
        [SerializeField] private GameObject m_goPrefab;
        [SerializeField] private GameObject m_goReward;
        [SerializeField] private GameObject m_goRankItem;
        [SerializeField] private GameObject m_goMyMsg;
        [SerializeField] private GameObject m_goProgressBoxItem;
        [SerializeField] private GameObject m_goGetScoreTip; //获取途径提示
        [SerializeField] private GameObject m_goCheckReward; //奖励查看提示
        [SerializeField] private GameObject m_goCheckRewardItem; //奖励查看item

        [SerializeField] private GameObject m_goPos;
        [SerializeField] private GameObject m_goRewardTip;
        [SerializeField] private GameObject m_goSoldierTip;
        [SerializeField] private GameObject m_goSoldierTaskTip;

        private VerticalLayoutGroup bottomLayout;
        private float rankItemHeight;
        private float spacing;

        private int lastTipInstanceId = 0;

        private UIButton scrollBtn;

        private long lastResetRequestTime = 0;//上一次倒计时归零重新请求数据的时间记录
        //------------------------------------------Top------------------------------------------
        [SerializeField] private RectTransform topRect;
        [SerializeField] private UIButton m_btnHelp;
        [SerializeField] private UIButton m_btnCalendar; //日历按钮
        [SerializeField] private UIText m_txtTitle; //标题
        [SerializeField] private UIImage m_imgIcon; //活动图标
        [SerializeField] private UIText m_txtRaceLevel; //竞赛段
        [SerializeField] private UIButton m_btnExtraBox; //额外宝箱
        [SerializeField] private Slider m_sliderProgress; //奖励进度
        [SerializeField] private UIText m_txtTimer; //活动倒计时
        [SerializeField] private GameObject m_goScore; //积分
        [SerializeField] private GameObject m_goProgressBox; //宝箱节点
        [SerializeField] private GameObject m_goExtraTip;

        //------------------------------------------Middle------------------------------------------
        [SerializeField] private RectTransform middleRect;


        //------------------------------------------Bottom------------------------------------------
        [SerializeField] private RectTransform bottomRect;
        [SerializeField] private GameObject m_goRankRoot;

        private ChaoZhiData ChaoZhiManager => GameEntry.LogicData.ChaoZhiData;
        private SpecialEventData SpecialEventManager => GameEntry.LogicData.SpecialEventData;

        private int timerCount; //倒计时数值
        private bool isTimer = false;

        private Dictionary<ulong, RoleBrief> RoleMsgDic;

        private ulong MyRoleId;

        //实例化对象时初始化
        public override void OnInit()
        {
            base.OnInit();
            MyRoleId = GameEntry.LogicData.RoleData.RoleId;
            m_goPrefab.SetActive(false);
            rankItemHeight = m_goRankItem.GetComponent<RectTransform>().rect.height;
            scrollBtn = m_scrollviewList.GetComponent<UIButton>();
            bottomLayout = m_goRankRoot.GetComponent<VerticalLayoutGroup>();
            spacing = bottomLayout.spacing;
            CloseLogic(1);
            var canvasGroups = m_goExtraTip.GetComponentsInChildren<CanvasGroup>(true);
            foreach (var com in canvasGroups)
            {
                com.alpha = 0;
            }
            m_goExtraTip.SetActive(false);

            if (RoleMsgDic == null)
            {
                RoleMsgDic = new Dictionary<ulong, RoleBrief>();
            }
            else
            {
                RoleMsgDic.Clear();
            }
            
            m_goSoldierTip.SetActive(false);
            SetPageNode(true);
            m_btnExtraBox.interactable = true;
            //说明按钮
            BindBtnLogic(m_btnHelp, () =>
            {
                GameEntry.UI.OpenUIForm(EnumUIForm.UIComDescForm, 22);
                CloseLogic(1);
            });

            //日历按钮
            BindBtnLogic(m_btnCalendar, () =>
            {
                GameEntry.UI.OpenUIForm(EnumUIForm.UIArmsRaceCalendarForm);
                CloseLogic(1);
            });

            //额外宝箱奖励按钮
            BindBtnLogic(m_btnExtraBox, () =>
            {
                //奖章宝箱
                if (!ToolScriptExtend.GetTable<race_box_reward>(out var config)) return;
                var showMedalBoxId = SpecialEventManager.GetNextMedalBoxId();
                var curConfig = config.FirstOrDefault(x => x.id == showMedalBoxId);
                if (curConfig != null)
                {
                    var logicId = curConfig.id;
                    //0:未解锁 1：可领取 2：已领取
                    var status = SpecialEventManager.CheckMedalBoxStatus(logicId, curConfig.star);
                    if (status == 1)
                    {
                        SpecialEventManager.C2SRaceBoxDrawReq(logicId);
                    }
                    else
                    {
                        ShowExtraBoxTip();
                    }
                }
            });

            BindBtnLogic(scrollBtn, () => { CloseLogic(1, 3); });

            ShowMedalEntryProgress();
            InitPageView();
            RequestSumMsg();
        }

        private void RequestSumMsg()
        {
            SpecialEventManager.C2SRaceRankReq(() =>
            {
                var roleList = SpecialEventManager.GetRankList();
                if (roleList.Count > 0)
                {
                    List<RoleMeta> roleMetaList = new List<RoleMeta>();
                    foreach (var role in roleList)
                    {
                        roleMetaList.Add(new RoleMeta() { RoleId = role.RoleId, ServerId = role.ServerId });
                    }
                    ColorLog.PowerfulJsonDebug(roleMetaList,"请求玩家列表");
                    GameEntry.LogicData.RoleData.RequestRoleQueryMulti(roleMetaList, (roleResp) =>
                    {
                        foreach (var info in roleResp.Roles)
                        {
                            RoleMsgDic[info.Id] = info;
                        }
                        
                        
                        
                        ColorLog.PowerfulJsonDebug(roleResp,"玩家详情数据");
                        
                        SpecialEventManager.C2SRaceInfoReq();
                    });
                }
            });
        }
        
        //再次打开
        public override void OnReOpen()
        {
            lastTipInstanceId = 0;
            CloseLogic(1);
            m_goExtraTip.SetActive(false);
            m_scrollviewList.normalizedPosition = new Vector2(0, 1);
            SetPageNode(true);
            m_btnExtraBox.interactable = true;
        }

        //本界面刷新，一般由UISwitchPage.RefreshCurPage触发
        public override void OnRefreshSelf()
        {
            base.OnRefreshSelf();
        }

        //事件刷新
        public override void OnRefresh(object userData)
        {
            var data = (ValueTuple<string, int>)userData;
            var flag = data.Item1;
            var param = data.Item2;
            if (flag == "SpecialEvent_ArmsRace")
            {
                if (param == 1)
                {
                    InitPageView();
                }
                else if (param == 2)
                {
                    ShowMedalEntryProgress();
                }
            }
        }

        /// <summary>
        /// 被选中触发的逻辑
        /// </summary>
        /// <param name="isOn">是否被选中</param>
        public override void OnSelect(bool isOn)
        {
        }

        //隐藏面板时逻辑
        public override void OnClose()
        {
            CloseLogic(1, 2, 3);
            SetPageNode(false);
        }

        //每帧更新逻辑，类似于Unity的Update函数
        public override void OnUpdate()
        {
        }

        //计时器逻辑
        public override void OnTimer()
        {
            if (!isTimer) return;
            var temp = timerCount - 1;
            if (temp >= 0)
            {
                timerCount--;
                m_txtTimer.text = ToolScriptExtend.FormatTime(timerCount);
                if (timerCount == 0)
                {
                    isTimer = false;
                    //初始化请求
                    m_txtTimer.text = "00:00:00";

                    var offset = TimeComponent.Now - lastResetRequestTime;
                    if (offset > 5 * 60)
                    {
                        RequestSumMsg();
                        lastResetRequestTime = TimeComponent.Now;
                    }
                }
            }
        }

        //资源释放
        public override void Release()
        {
        }

        private void InitPageView()
        {
            var rankList = SpecialEventManager.GetRankList();

            var test = rankList.Select(x => new { x.Score, x.UpdateTime, x.RoleId }).ToList();
            ColorLog.PowerfulJsonDebug(test,"排行榜排序测试");
            
            var rankCount = rankList.Count;
            CalculateContentHeight(rankCount);

            //------------------------------------------Top------------------------------------------

            var typeConfig = GameEntry.LogicData.SpecialEventData.ActivitySet.race_type_language;
            var timeId = SpecialEventManager.GetCurRaceTimeId();
            if (ToolScriptExtend.GetConfigById<race_time>(timeId, out var timeConfig))
            {
                m_txtTitle.text = ToolScriptExtend.GetLang(int.Parse(typeConfig[(int)timeConfig.arms_race_type - 1]));
                m_imgIcon.SetImage(timeConfig.racw_theme_image);
            }

            //计算倒计时
            timerCount = CalculateTimeCount();
            isTimer = timerCount > 0;
            m_txtTimer.text = ToolScriptExtend.FormatTime(timerCount);

            if (SpecialEventManager.RaceRankMsg != null)
            {
                var minLevel = SpecialEventManager.RaceRankMsg.MinLev;
                var maxLevel = SpecialEventManager.RaceRankMsg.MaxLev;
                m_txtRaceLevel.text = minLevel == maxLevel ? $"{ToolScriptExtend.GetLang(1100528)}: Lv{minLevel}" : $"{ToolScriptExtend.GetLang(1100528)}: Lv{minLevel}-{maxLevel}";
            }

            //奖章宝箱
            ShowExtraBoxProgress();
            
            //主要宝箱
            var curRaceMainId = SpecialEventManager.GetCurRaceMainId();
            var curMainScore = SpecialEventManager.GetCurRaceMainScore();
            m_goScore.transform.Find("txt").GetComponent<UIText>().text = curMainScore.ToString();

            if (ToolScriptExtend.GetConfigById<race_main>(curRaceMainId, out var raceMainData))
            {
                var boxRoot = m_goProgressBox.transform;

                var boxCount = 3;
                ToolScriptExtend.RecycleOrCreate(m_goProgressBoxItem, boxRoot, boxCount);
                for (var i = 0; i < boxCount; i++)
                {
                    var child = boxRoot.GetChild(i);
                    SetMainBoxInfo(i, child.gameObject, raceMainData);
                }

                //进度分数
                var scoreList = new List<int>
                    { raceMainData.first_box_score, raceMainData.second_box_score, raceMainData.third_box_score };
                for (var i = 0; i < scoreList.Count; i++)
                {
                    var txt = m_sliderProgress.transform.Find($"score{i + 1}/Text").GetComponent<UIText>();
                    txt.text = scoreList[i].ToString();
                }

                m_sliderProgress.value = SpecialEventManager.CalculateProgress(scoreList,
                    new List<float>() { 0.16f, 0.42f, 0.42f },
                    curMainScore);
            }

            //------------------------------------------Middle------------------------------------------
            if (ToolScriptExtend.GetTable<race_task>(out var taskTable))
            {
                var result = new List<race_task>();
                if (timeConfig.arms_race_type == arms_racetype.arms_racetype_soldier)
                {
                    var taskList = taskTable.Where(x => x.arms_race_type == timeConfig.arms_race_type).ToList();
                    var maxSoldierLevel = GameEntry.LogicData.BuildingData.GetSoldierMaxLevel();

                    if (maxSoldierLevel == 0)
                    {
                        maxSoldierLevel = 1;
                    }
                    foreach (var temp in taskList)
                    {
                        if (temp.affair_type == affairtype.affairtype_1044)
                        {
                            if (temp.task_value.Count >= 2 && int.Parse(temp.task_value[1]) == maxSoldierLevel)
                            {
                                result.Add(temp);
                            }
                        }
                        else
                        {
                            result.Add(temp);
                        }
                    }
                    result.Sort((a, b) => a.id - b.id);
                }
                else
                {
                    result.AddRange(taskTable.Where(x => x.arms_race_type == timeConfig.arms_race_type));
                }
                
                var taskCount = result.Count;
                var getWayRoot = middleRect.transform.Find("node");
                ToolScriptExtend.RecycleOrCreate(m_goGetScoreTip, getWayRoot, taskCount);
                for (var i = 0; i < taskCount; i++)
                {
                    var child = getWayRoot.GetChild(i);
                    SetGetScoreWayInfo(i, child.gameObject, result[i], i == taskCount - 1);
                }
            }

            //------------------------------------------Bottom------------------------------------------
            var maxRankScore = 0;
            if (rankList.Count > 0)
            {
                maxRankScore = rankList[0].Score;
            }

            if (ToolScriptExtend.GetTable<race_rank_reward>(out var rankRewards))
            {
                var rankRoot = m_goRankRoot.transform;
                ToolScriptExtend.RecycleOrCreate(m_goRankItem, rankRoot, rankCount);
                for (var i = 0; i < rankCount; i++)
                {
                    var child = rankRoot.GetChild(i);
                    var logicId = i + 1;
                    var data = rankRewards.FirstOrDefault(x => (logicId >= x.min_rank && logicId <= x.max_rank));
                    var roleMsg = rankList[i];
                    SetRankInfo(i, child.gameObject, CheckIsMyself(roleMsg.RoleId), maxRankScore, rankList[i], data);
                }

                ToolScriptExtend.RecycleOrCreate(m_goRankItem, m_goMyMsg.transform, 1);
                var myObj = m_goMyMsg.transform.GetChild(0);
                var myRankMsg = rankList.FirstOrDefault(x => x.RoleId == MyRoleId);
                if (myRankMsg != null)
                {
                    if (RoleMsgDic.TryGetValue(MyRoleId, out var MyRoleData))
                    {
                        var myRankIndex = rankList.FindIndex(x => x.RoleId == MyRoleId);
                        var myLogicId = myRankIndex + 1;
                        var myRankRewardData =
                            rankRewards.FirstOrDefault(x => (myLogicId >= x.min_rank && myLogicId <= x.max_rank));
                        SetRankInfo(myRankIndex, myObj.gameObject, true, maxRankScore, myRankMsg, myRankRewardData);
                    }
                }
            }
        }

        //判断是否是本玩家
        private bool CheckIsMyself(ulong palyerId)
        {
            return GameEntry.LogicData.RoleData.RoleId == palyerId;
        }

        //设置宝箱显示
        private void SetMainBoxInfo(int index, GameObject obj, race_main raceMainData)
        {
            var root = obj.transform;
            var bg = root.Find("bg").GetComponent<UIImage>();
            var ok = root.Find("ok").GetComponent<UIImage>();
            var icon = root.Find("icon").GetComponent<UIImage>();
            var received = root.Find("received").GetComponent<UIImage>();
            var coin = root.Find("tip/coin").GetComponent<UIImage>();
            var value = root.Find("tip/value").GetComponent<UIText>();
            var btn = root.Find("btn").GetComponent<UIButton>();
            var pos = root.Find("pos");
            var effect = root.Find("effect");

            var logicId = index + 1;
            var configScore = 0;
            var configCost = 0;
            var configReward = new List<reward>();
            var effectPath = "";
            if (logicId == 1)
            {
                configScore = raceMainData.first_box_score;
                configCost = raceMainData.first_box_diamond_value;
                configReward = raceMainData.first_box_reward;
                effectPath = "battle_baoxiang_zhuanguang_bai";
            }
            else if (logicId == 2)
            {
                configScore = raceMainData.second_box_score;
                configCost = raceMainData.second_box_diamond_value;
                configReward = raceMainData.second_box_reward;
                effectPath = "battle_baoxiang_zhuanguang_zi";
            }
            else if (logicId == 3)
            {
                configScore = raceMainData.third_box_score;
                configCost = raceMainData.third_box_diamond_value;
                configReward = raceMainData.third_box_reward;
                effectPath = "battle_baoxiang_zhuanguang_huang";
            }

            value.text = configCost.ToString();

            //0:未解锁  1：可领取  2：已领取
            var status = SpecialEventManager.CheckTaskBoxStatus(logicId, configScore);

            icon.SetImage($"Sprite/ui_shijian/zbjs_icon_baoxiang3_{logicId}_1.png");
            received.SetImage($"Sprite/ui_shijian/zbjs_icon_baoxiang3_{logicId}.png");
            bg.SetImage($"Sprite/ui_shijian/zbjs_dikuang1_{logicId}.png");

            icon.gameObject.SetActive(status != 2);
            received.gameObject.SetActive(status == 2);
            ok.gameObject.SetActive(status == 1);
            effect.gameObject.SetActive(status == 1);

            if (!string.IsNullOrEmpty(effectPath))
            {
                if (effect.childCount == 0)
                {
                    ToolScriptExtend.LoadAndCreatePrefab($"Effect/Prefab/UI/{effectPath}", effect,
                        (effectObj) => { effectObj.transform.localPosition = Vector3.zero; });
                }
            }

            var boxAnim = icon.GetComponent<Animation>();
            if (boxAnim != null)
            {
                boxAnim.enabled = status == 1;
                boxAnim.Play("SpecialEvent_ArmsRace_box1");
            }

            BindBtnLogic(btn, () =>
            {
                if (status == 1)
                {
                    SpecialEventManager.C2SRaceTaskDrawReq(logicId, (articleList) =>
                    {
                        long count = 0;
                        foreach (var item in configReward)
                        {
                            if (item.item_id == itemid.itemid_1230111)
                            {
                                count += item.num;
                            }
                        }

                        if (count > 0)
                        {
                            var sequence = DOTween.Sequence();
                            sequence.AppendCallback(() =>
                            {
                                m_btnExtraBox.interactable = false;
                                FlyResManager.UIFlyByTrans(itemid.itemid_1230111, (int)count, btn.transform,
                                    m_btnExtraBox.transform);
                            });
                            sequence.AppendInterval(1);
                            sequence.AppendCallback(() =>
                            {
                                ShowMedalEntryProgress();
                            });
                            sequence.AppendInterval(0.5f);
                            
                            sequence.AppendCallback(() =>
                            {
                                m_goExtraTip.gameObject.SetActive(true);
                                PlayMedalBoxAnimTip(true);
                            });
                            sequence.AppendInterval(1.3f);
                            sequence.AppendCallback(() =>
                            {
                                PlayMedalBoxAnimTip(false);
                                m_btnExtraBox.interactable = true;
                            });
                        }
                    });
                }
                else
                {
                    ShowRewardTip(pos,
                        configReward,
                        index == 2,
                        true,
                        $"{ToolScriptExtend.GetLang(1100286)} {configCost}",
                        itemid.itemid_6);
                }
            });
        }

        //获取积分途径信息
        private void SetGetScoreWayInfo(int index, GameObject obj, race_task data, bool isEnd)
        {
            var root = obj.transform;
            var line = root.Find("line");
            var icon1 = root.Find("icon1").GetComponent<UIImage>();
            var icon2 = root.Find("icon2").GetComponent<UIImage>();
            var desc = root.Find("middle/desc").GetComponent<UIText>();
            var value = root.Find("value").GetComponent<UIText>();
            var mark = root.Find("mark");
            var btn = root.Find("btn").GetComponent<UIButton>();
            var soldier = root.Find("middle/soldier");
            var soldierBtn = soldier.Find("btn").GetComponent<UIButton>();
            var soldierPos = soldierBtn.transform.Find("pos");

            var isSpecial = data.affair_type == affairtype.affairtype_1044;
            icon1.gameObject.SetActive(isSpecial);
            icon2.gameObject.SetActive(!isSpecial);

            if (isSpecial)
            {
                icon1.SetImage(data.icon);
            }
            else
            {
                icon2.SetImage(data.icon);
            }

            mark.gameObject.SetActive(data.recommend);
            var descStr = ChaoZhiManager.GetTaskFormatStr(data.affair_desc, data.affair_type, data.task_value);
            desc.text = descStr;
            value.text = $"+{data.points}";
            line.gameObject.SetActive(!isEnd);
            soldier.gameObject.SetActive(isSpecial);

            //跳转
            BindBtnLogic(btn, () =>
            {
                // var data = new Race.PushRaceScoreChange()
                // {
                //     TimeId = 8,TaskBoxId = 701,OldTaskScore = 1000,NewTaskScore = 2500
                //
                // };
                // ToolScriptExtend.CheckAndRefreshForm<ValueTuple<string, object>>(EnumUIForm.UIScoreProgressForm,("SpecialEvent_ArmsRace",data));
            });

            BindBtnLogic(soldierBtn, () =>
            {
                CloseLogic(1);
                ShowSoldierMsgListTip(soldierPos.transform);
            });
        }

        //获取排名信息
        private void SetRankInfo(int index, GameObject obj, bool isMyself, int maxValue, RaceRoleRank msg,
            race_rank_reward rankReward)
        {
            if (!RoleMsgDic.ContainsKey(msg.RoleId)) return;

            var root = obj.transform;
            var icon = root.Find("rank").GetComponent<UIImage>();
            var txt = root.Find("index").GetComponent<UIText>();
            var boxIcon = root.Find("box").GetComponent<UIImage>();
            var boxBtn = root.Find("box").GetComponent<UIButton>();
            var bg = root.GetComponent<UIImage>();
            var slider = root.Find("slider").GetComponent<Slider>();
            var value = root.Find("slider/value").GetComponent<UIText>();
            var desc = root.Find("desc").GetComponent<UIText>();
            var pos = boxBtn.transform.Find("pos");
            var level = root.Find("head/Image/Text").GetComponent<UIText>();

            var descRect = desc.GetComponent<RectTransform>();
            var sex1 = root.Find("sex1").GetComponent<UIImage>();
            var sex2 = root.Find("sex2").GetComponent<UIImage>();

            var roleMsg = RoleMsgDic[msg.RoleId];
            var rankIndex = index + 1;
            sex1.gameObject.SetActive(roleMsg.Gender == 1);
            sex2.gameObject.SetActive(roleMsg.Gender == 2);
            descRect.anchoredPosition = roleMsg.Gender == 0 ? new Vector2(255, -20) : new Vector2(318, -20);

            var isTop3 = rankIndex is 1 or 2 or 3;
            icon.gameObject.SetActive(isTop3);
            if (isTop3)
            {
                icon.SetImage($"Sprite/ui_youjian/paiming_icon{rankIndex}.png");
            }

            var scoreValue = msg.Score;
            txt.text = rankIndex.ToString();

            var hasRewardBox = scoreValue > 0 && rankReward != null; //是否有奖励宝箱
            var bgPath = isMyself ? "zbjs_dikuang4" : "zbjs_dikuang3";
            bg.SetImage($"Sprite/ui_shijian/{bgPath}.png");
            level.text = roleMsg.HeadquartersLevel.ToString();
            boxIcon.gameObject.SetActive(hasRewardBox);
            if (hasRewardBox)
            {
                boxIcon.SetImage(rankReward.icon);
            }

            var title = "";
            var hex = isMyself ? "#3A864E" : "#21292D";
            if (roleMsg.UnionId > 0)
            {
                title += roleMsg.ServerName;
                title += " ";
                title += roleMsg.Name;
                desc.text = $"<color={hex}>{title}</color>";
                
                GameEntry.LogicData.UnionData.OnReqUnionBrief(roleMsg.UnionId, (unionBrief) =>
                {
                    title = "";
                    title += roleMsg.ServerName;
                    title += $" [{unionBrief.Name}]";
                    title += roleMsg.Name;
                    desc.text = $"<color={hex}>{title}</color>";
                });
            }
            else
            {
                title += roleMsg.ServerName;
                title += " ";
                title += roleMsg.Name;
                desc.text = $"<color={hex}>{title}</color>";
            }
            
            if (maxValue <= 0)
            {
                maxValue = 1;
            }

            if (scoreValue >= maxValue)
            {
                slider.value = 1;
            }
            else
            {
                slider.value = scoreValue * 1.0f / maxValue;
            }

            value.text = scoreValue.ToString();
            boxIcon.gameObject.SetActive(hasRewardBox);

            BindBtnLogic(boxBtn, () =>
            {
                if (rankReward != null)
                {
                    ShowRewardTip(pos, rankReward.rank_reward, true);
                }
            });
        }

        private void CalculateContentHeight(int rankCount)
        {
            float sum = 0;
            sum += topRect.rect.height;
            sum += middleRect.rect.height;

            float bottomHeight = rankItemHeight * rankCount + spacing * (rankCount - 1) + bottomLayout.padding.bottom;
            bottomRect.SetSizeWithCurrentAnchors(RectTransform.Axis.Vertical, bottomHeight);

            float offset = 10;
            sum += offset;
            sum += bottomHeight;
            m_scrollviewList.content.SetSizeWithCurrentAnchors(RectTransform.Axis.Vertical, sum);
        }
        
        //查看额外宝箱奖励提示
        private void ShowExtraBoxTip()
        {
            var sliderCanvas = m_goExtraTip.transform.Find("slider").GetComponent<CanvasGroup>();
            var isOpen = sliderCanvas.alpha > 0;
            m_goExtraTip.gameObject.SetActive(true);
            if (isOpen)
            {
                CloseLogic(1, 2);
            }
            else
            {
                CloseLogic(1);
            }

            PlayMedalBoxAnimTip(!isOpen);
        }

        private void PlayMedalBoxAnimTip(bool isOpen)
        {
            var anim = m_goExtraTip.GetComponent<Animation>();
            if (anim != null)
            {
                var clip = isOpen ? "SpecialEvent_ArmsRace_box_open" : "SpecialEvent_ArmsRace_box_closed";
                anim.Play(clip);
            }
            SetMedalTipInteractable(isOpen);
        }

        //显示额外宝箱进度
        private void ShowExtraBoxProgress()
        {
            if (!ToolScriptExtend.GetTable<race_box_reward>(out var rewardConfig)) return;

            var root = m_goExtraTip.transform;
            var slider = root.Find("slider").GetComponent<Slider>();
            var score = root.Find("Image/value").GetComponent<UIText>();

            var curScore = SpecialEventManager.GetCurMedalScore();
            score.text = curScore.ToString();

            slider.value = SpecialEventManager.CalculateProgress(rewardConfig.Select(x => x.star).ToList(),
                new List<float>() { 0.33f, 0.33f, 0.34f },
                curScore);

            for (var i = 0; i < rewardConfig.Count; i++)
            {
                var data = rewardConfig[i];
                var logicId = i + 1;
                var child = root.Find($"box/box{logicId}");
                var icon = child.Find("icon").GetComponent<UIImage>();
                var received = child.Find("received").GetComponent<UIImage>();
                var txt = child.Find("txt").GetComponent<UIText>();
                var btn = child.Find("btn").GetComponent<UIButton>();
                var pos = child.Find("pos");
                var effect = child.Find("effect");

                icon.SetImage($"Sprite/ui_shijian/zbjs_icon_baoxiang1_{logicId}_1.png");
                received.SetImage($"Sprite/ui_shijian/zbjs_icon_baoxiang1_{logicId}_2.png");

                txt.text = data.star.ToString();
                //0:未解锁 1：可领取 2：已领取
                var status = SpecialEventManager.CheckMedalBoxStatus(data.id, data.star);
                icon.gameObject.SetActive(status != 2);
                received.gameObject.SetActive(status == 2);
                // effect.gameObject.SetActive(status == 1);

                BindBtnLogic(btn, () =>
                {
                    if (status == 1)
                    {
                        SpecialEventManager.C2SRaceBoxDrawReq(data.id);
                    }
                    else
                    {
                        ShowRewardTip(pos, data.daily_box_reward, true);
                    }
                });
            }
        }

        //查看奖励弹窗
        private void ShowRewardTip(Transform startTrans, List<reward> rewards, bool showRight = false,
            bool hasTitle = false, string str = "",
            itemid id = itemid.itemid_1)
        {
            CloseLogic(3);
            var instanceId = startTrans.GetInstanceID();
            if (instanceId == lastTipInstanceId)
            {
                CloseLogic(1);
                lastTipInstanceId = 0;
                return;
            }

            m_goRewardTip.gameObject.SetActive(true);
            SetScrollEnable(false);

            lastTipInstanceId = instanceId;

            var duration = 0.2f;

            var anchorPos = GetAnchorPos(startTrans);
            var tipRect = m_goRewardTip.GetComponent<RectTransform>();

            tipRect.pivot = showRight ? new Vector2(1, 0.35f) : new Vector2(0, 0.35f);
            m_goCheckReward.transform.Find("angleLeft").gameObject.SetActive(!showRight);
            m_goCheckReward.transform.Find("angleRight").gameObject.SetActive(showRight);
            tipRect.anchoredPosition = new Vector2(anchorPos.x, anchorPos.y);

            var root = m_goCheckReward.transform;
            var title = root.Find("title").GetComponent<UIText>();
            title.gameObject.SetActive(hasTitle);
            if (hasTitle)
            {
                title.text = str;
                var icon = root.Find("title/icon").GetComponent<UIImage>();
                icon.SetImage(ToolScriptExtend.GetItemIcon(id));
            }

            var scroll = root.Find("reward/Scroll View").GetComponent<ScrollRect>();
            var contentRoot = scroll.content.transform;

            ToolScriptExtend.RecycleOrCreate(m_goCheckRewardItem, contentRoot, rewards.Count);
            for (var i = 0; i < rewards.Count; i++)
            {
                var data = rewards[i];
                var child = contentRoot.GetChild(i);
                var node = child.Find("node");
                var txt = child.Find("txt").GetComponent<UIText>();
                var count = child.Find("count").GetComponent<UIText>();
                txt.text = ToolScriptExtend.GetItemName(data.item_id);
                count.text = ToolScriptExtend.FormatNumberWithUnit(data.num);

                ToolScriptExtend.RecycleOrCreate(m_goReward, node, 1);
                var rewardChild = node.GetChild(0);
                var rewardNode = rewardChild.Find("node");
                ToolScriptExtend.LoadReward(rewardNode, data, null, 0.5f);
            }

            float topHeight = hasTitle ? 103 : 30;
            float bottomHeight = 33f;
            float rewardItemHeight = m_goCheckRewardItem.GetComponent<RectTransform>().rect.height;
            float sum = 0;
            sum += topHeight;
            sum += bottomHeight;

            var rewardRect = root.Find("reward").GetComponent<RectTransform>();

            var listCount = Math.Clamp(rewards.Count, 3, 6);
            var listHeight = listCount * rewardItemHeight + (listCount - 1) * 10 + 10;
            rewardRect.SetSizeWithCurrentAnchors(RectTransform.Axis.Vertical, listHeight);
            rewardRect.anchoredPosition = new Vector2(0, -topHeight);

            sum += listHeight;
            m_goCheckReward.GetComponent<RectTransform>().SetSizeWithCurrentAnchors(RectTransform.Axis.Vertical, sum);
        }

        private Vector2 GetAnchorPos(Transform target)
        {
            m_goPos.transform.position = target.position;
            var rect = m_goPos.GetComponent<RectTransform>();
            return rect.anchoredPosition;
        }

        private void CloseLogic(params int[] type)
        {
            if (type.Contains(1))
            {
                m_goRewardTip.SetActive(false);
                SetScrollEnable(true);
            }

            if (type.Contains(2))
            {
                PlayMedalBoxAnimTip(false);
            }

            if (type.Contains(3))
            {
                m_goSoldierTip.SetActive(false);
                SetScrollEnable(true);
            }
        }

        private void SetScrollEnable(bool isEnable)
        {
            m_scrollviewList.enabled = isEnable;
            scrollBtn.enabled = !isEnable;
        }

        private void ShowSoldierMsgListTip(Transform target)
        {
            var status = !m_goSoldierTip.activeInHierarchy;
            m_goSoldierTip.SetActive(!m_goSoldierTip.activeInHierarchy);

            if (status)
            {
                var anchorPos = GetAnchorPos(target);
                var tipRect = m_goSoldierTip.GetComponent<RectTransform>();
                tipRect.anchoredPosition = anchorPos;
            }

            SetScrollEnable(!status);

            var root = m_goSoldierTip.transform.Find("reward/Scroll View/Viewport/Content");
            var scroll = m_goSoldierTip.transform.Find("reward/Scroll View").GetComponent<ScrollRect>();
            if (ToolScriptExtend.GetTable<race_task>(out var table))
            {
                var list = table.Where(x => x.affair_type == affairtype.affairtype_1044).ToList();
                ToolScriptExtend.RecycleOrCreate(m_goSoldierTaskTip, root, list.Count);
                for (var i = 0; i < list.Count; i++)
                {
                    var data = list[i];
                    var child = root.GetChild(i);
                    var desc = child.Find("desc").GetComponent<UIText>();
                    var value = child.Find("value").GetComponent<UIText>();

                    var str = ChaoZhiManager.GetTaskFormatStr(data.affair_desc, data.affair_type, data.task_value);
                    desc.text = str;
                    value.text = $"+{data.points}";
                }
            }

            scroll.normalizedPosition = new Vector2(0, 1);
        }


        private void SetPageNode(bool setParent)
        {
            if (setParent)
            {
                var form = GetBindForm<UISpecialEventForm>();
                if (form != null)
                {
                    form.BindTipNode(m_goSoldierTip.transform);
                    form.BindTipNode(m_goRewardTip.transform);
                }
            }
            else
            {
                m_goSoldierTip.transform.SetParent(m_goPrefab.transform);
                m_goRewardTip.transform.SetParent(m_goPrefab.transform);
            }
        }

        //计算倒计时时间
        private int CalculateTimeCount()
        {
            var timeId = SpecialEventManager.GetCurRaceTimeId();
            if (ToolScriptExtend.GetConfigById<race_time>(timeId, out var timeConfig))
            {
                var timeValue = timeConfig.arms_open_rime.Replace("\"", "");
                var result = GameEntry.LogicData.SpecialEventData.GetRaceUTCTime(timeValue);
                var newResult = result.AddHours(4);
                DateTimeOffset dto = new DateTimeOffset(newResult);
                var nextTime = dto.ToUnixTimeSeconds(); 
                var offset = nextTime - (long)TimeComponent.Now;
                offset %= 86400;
                return (int)offset;
            }
            return 0;
        }

        //显示奖励奖章宝箱入口进度逻辑
        private void ShowMedalEntryProgress()
        {
            //奖章宝箱
            if (!ToolScriptExtend.GetTable<race_box_reward>(out var config)) return;
            var showMedalBoxId = SpecialEventManager.GetNextMedalBoxId();
            var curConfig = config.FirstOrDefault(x => x.id == showMedalBoxId);
            if (curConfig == null) return;
            var root = m_btnExtraBox.transform;
            var icon = root.Find("icon").GetComponent<UIImage>();
            var received = root.Find("received").GetComponent<UIImage>();
            var slider = root.Find("slider").GetComponent<Slider>();
            var value = root.Find("value").GetComponent<UIText>();
            var effect = root.Find("effect");

            var logicId = curConfig.id;
            //0:未解锁 1：可领取 2：已领取
            var status = SpecialEventManager.CheckMedalBoxStatus(logicId, curConfig.star);

            icon.SetImage($"Sprite/ui_shijian/zbjs_icon_baoxiang1_{logicId}_1.png");
            received.SetImage($"Sprite/ui_shijian/zbjs_icon_baoxiang1_{logicId}_2.png");

            icon.gameObject.SetActive(status != 2);
            received.gameObject.SetActive(status == 2);
            effect.gameObject.SetActive(status == 1);
            var curMedalScore = SpecialEventManager.GetCurMedalScore();

            var boxAnim = icon.GetComponent<Animation>();
            if (boxAnim != null)
            {
                boxAnim.enabled = status == 1;
                boxAnim.Play("SpecialEvent_ArmsRace_box2");
            }

            value.text = $"{curMedalScore}/{curConfig.star}";
            var ratio = curMedalScore * 1.0f / curConfig.star;
            if (ratio > 1)
            {
                ratio = 1;
            }

            slider.value = ratio;
        }

        private void SetMedalTipInteractable(bool isEnable)
        {
            var canvasGroups = m_goExtraTip.GetComponentsInChildren<CanvasGroup>(true);
            foreach (var com in canvasGroups)
            {
                com.interactable = isEnable;
                com.blocksRaycasts = isEnable;
            }
        }
    }
}